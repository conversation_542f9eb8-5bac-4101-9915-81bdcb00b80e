#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析实际请求参数，找出正确的AppId和加密逻辑
"""

import requests
import execjs
import json
import time
from bs4 import BeautifulSoup
import base64


class RequestAnalyzer:
    def __init__(self):
        # 读取加密JS文件
        with open('encrypt.js', 'r', encoding='utf-8') as f:
            js_code = f.read()
        
        # 编译JS代码
        self.ctx = execjs.compile(js_code)
        
        # 会话对象
        self.session = requests.Session()
        
        # 从实际请求中提取的信息
        self.actual_request = "ZKk+3tHjraXNmZ2vaCtKwLuCClOBe08xbBK6ImY5WBzfnKrnOymdKu2x073g+gHq5uqCpSH+WQYkr6o51llrt9xkVKTQhLGLJo2Rk0Rk6aJ0vVNpU1suhIOmtitbFZsv3I6eJPQZMBES1lMdASiOkg=="
        self.actual_ctxid = "98156d95be78f8855f058463b0be37d3"
        
        # 可能的AppId列表
        self.possible_appids = [
            "***********",  # 我们之前使用的
            "***********1",  # 可能的变体
            "********",     # 简化版本
            "",             # 空值
            "appid",        # 字面值
        ]

    def test_different_appids(self):
        """
        测试不同的AppId，看哪个能生成有效的请求
        """
        print("=== 测试不同的AppId ===")
        
        # 测试参数
        account = "***********"
        password = "123456"
        
        for appid in self.possible_appids:
            print(f"\n测试AppId: '{appid}'")
            
            try:
                # 构建请求参数
                params = self.ctx.call('CaptchaEncrypt.buildCaptchaGetParams',
                                     self.actual_ctxid,
                                     appid,
                                     account,
                                     password)
                
                # 发送测试请求
                url = "https://smartvcode2.eastmoney.com/Titan/api/captcha/get"
                headers = {
                    "Accept": "*/*",
                    "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
                    "Connection": "keep-alive",
                    "Referer": "https://exaccount2.eastmoney.com/",
                    "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
                }
                
                response = self.session.get(url, headers=headers, params=params)
                
                print(f"  状态码: {response.status_code}")
                print(f"  响应: {response.text}")
                
                # 解析响应
                if response.status_code == 200:
                    try:
                        json_str = response.text.replace('cb(', '').rstrip(')')
                        result = json.loads(json_str)
                        
                        if result.get('ReturnCode') != '6':  # 6表示AppId无效
                            print(f"  ✅ AppId '{appid}' 可能有效!")
                            return appid
                        else:
                            print(f"  ❌ AppId '{appid}' 无效")
                    except:
                        print(f"  ❌ 响应解析失败")
                
            except Exception as e:
                print(f"  ❌ 测试失败: {e}")
        
        return None

    def analyze_actual_request(self):
        """
        分析实际请求参数的结构
        """
        print("=== 分析实际请求参数 ===")
        
        try:
            # 尝试解码base64
            decoded = base64.b64decode(self.actual_request)
            print(f"Base64解码结果长度: {len(decoded)} 字节")
            print(f"前50字节: {decoded[:50]}")
            
            # 尝试解密（这需要知道正确的密钥）
            # 由于我们不知道确切的解密方法，这里只是展示结构
            
        except Exception as e:
            print(f"分析失败: {e}")

    def test_with_real_ctxid(self):
        """
        使用真实的ctxid进行测试
        """
        print("=== 使用真实ctxid测试 ===")
        
        try:
            # 获取真实的ctxid
            ctxid = self.get_real_ctxid()
            if not ctxid:
                print("❌ 无法获取真实ctxid")
                return
            
            print(f"获取到真实ctxid: {ctxid}")
            
            # 使用不同的AppId测试
            account = "***********"
            password = "123456"
            
            for appid in self.possible_appids:
                print(f"\n测试AppId: '{appid}' with 真实ctxid")
                
                try:
                    params = self.ctx.call('CaptchaEncrypt.buildCaptchaGetParams',
                                         ctxid,
                                         appid,
                                         account,
                                         password)
                    
                    url = "https://smartvcode2.eastmoney.com/Titan/api/captcha/get"
                    headers = {
                        "Accept": "*/*",
                        "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
                        "Connection": "keep-alive",
                        "Referer": "https://exaccount2.eastmoney.com/",
                        "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
                    }
                    
                    response = self.session.get(url, headers=headers, params=params)
                    print(f"  响应: {response.text}")
                    
                    if response.status_code == 200:
                        try:
                            json_str = response.text.replace('cb(', '').rstrip(')')
                            result = json.loads(json_str)
                            
                            if result.get('ReturnCode') != '6':
                                print(f"  ✅ 找到有效的AppId: '{appid}'")
                                return appid
                        except:
                            pass
                
                except Exception as e:
                    print(f"  ❌ 测试失败: {e}")
        
        except Exception as e:
            print(f"测试失败: {e}")
        
        return None

    def get_real_ctxid(self):
        """
        获取真实的ctxid
        """
        try:
            params = {'rc': str(int(time.time() * 1000))}
            
            page_headers = {
                "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
                "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
                "Connection": "keep-alive",
                "Referer": "https://passport2.eastmoney.com/",
                "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
            }
            
            response = self.session.get("https://exaccount2.eastmoney.com/home/<USER>", 
                                      headers=page_headers, 
                                      params=params)
            
            if response.status_code == 200:
                soup = BeautifulSoup(response.text, 'html.parser')
                input_element = soup.find('input', {'id': 'hdAccountCaptContextId'})
                
                if input_element:
                    return input_element.get('value')
            
            return None
            
        except Exception as e:
            print(f"获取ctxid失败: {e}")
            return None

    def test_without_appid(self):
        """
        测试不使用AppId的情况
        """
        print("=== 测试不使用AppId ===")
        
        try:
            # 获取真实ctxid
            ctxid = self.get_real_ctxid()
            if not ctxid:
                print("❌ 无法获取真实ctxid")
                return
            
            # 直接构建简单的请求字符串
            account = "***********"
            password = "123456"
            
            # 尝试不同的字符串格式
            test_formats = [
                f"ctxid={ctxid}|a={account}|p={password}|r={0.*********}",
                f"ctxid={ctxid}|account={account}|password={password}|random={0.*********}",
                f"{ctxid}|{account}|{password}|{0.*********}",
                f"a={account}|p={password}|ctxid={ctxid}|r={0.*********}",
            ]
            
            for i, test_str in enumerate(test_formats):
                print(f"\n测试格式 {i+1}: {test_str}")
                
                try:
                    # 直接加密和编码
                    encrypted = self.ctx.call('CaptchaEncrypt.encrypt', test_str)
                    encoded = self.ctx.call('CaptchaEncrypt.base64Encode', encrypted)
                    
                    params = {
                        "callback": "cb",
                        "ctxid": ctxid,
                        "request": encoded,
                        "_": str(int(time.time() * 1000))
                    }
                    
                    url = "https://smartvcode2.eastmoney.com/Titan/api/captcha/get"
                    headers = {
                        "Accept": "*/*",
                        "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
                        "Connection": "keep-alive",
                        "Referer": "https://exaccount2.eastmoney.com/",
                        "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
                    }
                    
                    response = self.session.get(url, headers=headers, params=params)
                    print(f"  响应: {response.text}")
                    
                    if response.status_code == 200:
                        try:
                            json_str = response.text.replace('cb(', '').rstrip(')')
                            result = json.loads(json_str)
                            
                            if result.get('ReturnCode') != '6':
                                print(f"  ✅ 格式 {i+1} 可能有效!")
                                return test_str
                        except:
                            pass
                
                except Exception as e:
                    print(f"  ❌ 测试失败: {e}")
        
        except Exception as e:
            print(f"测试失败: {e}")
        
        return None

    def run_analysis(self):
        """
        运行完整分析
        """
        print("开始分析实际请求参数")
        print("=" * 60)
        
        # 1. 分析实际请求结构
        self.analyze_actual_request()
        
        print("\n" + "-" * 40 + "\n")
        
        # 2. 测试不同的AppId
        valid_appid = self.test_different_appids()
        
        print("\n" + "-" * 40 + "\n")
        
        # 3. 使用真实ctxid测试
        if not valid_appid:
            valid_appid = self.test_with_real_ctxid()
        
        print("\n" + "-" * 40 + "\n")
        
        # 4. 测试不使用AppId
        if not valid_appid:
            valid_format = self.test_without_appid()
        
        print("\n" + "=" * 60)
        if valid_appid:
            print(f"🎉 找到有效的AppId: '{valid_appid}'")
        else:
            print("❌ 未找到有效的AppId，可能需要进一步分析")


def main():
    analyzer = RequestAnalyzer()
    analyzer.run_analysis()


if __name__ == "__main__":
    main()
