import requests


headers = {
    "Accept": "application/json, text/javascript, */*; q=0.01",
    "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
    "Connection": "keep-alive",
    "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
    "Origin": "https://exaccount2.eastmoney.com",
    "Referer": "https://exaccount2.eastmoney.com/home/<USER>",
    "RequestVerificationToken": "q-uCq_YOPkYrcWGOlLoLmPjB_DwMVtz_DUc6JDE6wMohsBO-TuSczOIiXFQ03YDfck3ZmnYunfQBNP7M36xtRbLyDn41",
    "Sec-Fetch-Dest": "empty",
    "Sec-Fetch-Mode": "cors",
    "Sec-Fetch-Site": "same-origin",
    "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
    "X-Requested-With": "XMLHttpRequest",
    "deviceType": "Web",
    "domainName": "passport2.eastmoney.com",
    "productType": "UserPassport",
    "sec-ch-ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"",
    "sec-ch-ua-mobile": "?0",
    "sec-ch-ua-platform": "\"macOS\""
}
cookies = {
    "qgqp_b_id": "7a333970f6cb928e27b8199386fff877",
    "websitepoptg_api_time": "*************",
    "st_si": "**************",
    "p_origin": "https%3A%2F%2Fpassport2.eastmoney.com",
    "RequestData": "%7b%22agentPageUrl%22%3a%22https%3a%2f%2fpassport2.eastmoney.com%2fpub%2fLoginAgent%22%2c%22redirectUrl%22%3a%22https%3a%2f%2fwww.eastmoney.com%2f%22%2c%22callBack%22%3a%22LoginCallBack%22%2c%22redirectFunc%22%3a%22PageRedirect%22%2c%22data%22%3a%7b%22domainName%22%3a%22passport2.eastmoney.com%22%2c%22deviceType%22%3a%22Web%22%2c%22productType%22%3a%22UserPassport%22%2c%22version%22%3a%220.0.1%22%7d%2c%22type%22%3anull%7d",
    "__RequestVerificationToken": "bEA4kpphJASaT5KRxdqv-qTIRN_MGI8rOezgTigQUJVPg5_1rmkDmKOelXDA3nejG5mxJTcxRifCR7a8b5rFtFQwpAk1",
    "st_asi": "delete",
    "st_pvi": "**************",
    "st_sp": "2025-07-28%2010%3A24%3A43",
    "st_inirUrl": "https%3A%2F%2Fcn.bing.com%2F",
    "st_sn": "13",
    "st_psi": "*****************-************-**********",
    "_qct": "b7b556e9fb114afb8622e6c2d0d0b848",
    "_qcu": "117.140.252.779c4ce40b"
}
url = "https://exaccount2.eastmoney.com/JsonAPI/Login3"
data = {
    "username": "***********",
    "password": "seR+/T7spdawZlM8PeIY0LtPsijkzVJ9cf0nh+SasZiYf989e2GHVr/h6BgIiuv6IWW0kN4OJhdJAtY3Xm7t2fwtk3q8twbL7FfAWEMcoijiJrlJqZtjW3fTrfPqNb1000SOFEMBAwG0YXrDGoO4DGpRYBAgYPsZfht8nSoK4Fk=",
    "captconetxt": "d6ac405cb3cd727ee64fc00b1f747429",
    "captvalidate": "645973bac38377cb727f40f50102f5b4"
}
response = requests.post(url, headers=headers, cookies=cookies, data=data)

print(response.text)
print(response)