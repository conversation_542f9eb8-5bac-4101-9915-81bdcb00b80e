#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用真正的RSA加密实现东方财富网密码加密
"""

import base64
from Crypto.PublicKey import RSA
from Crypto.Cipher import PKCS1_v1_5
import binascii


class RealRSAEncrypt:
    def __init__(self):
        # 东方财富网的RSA公钥（Base64格式）
        self.public_key_base64 = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDBgxenWGQrynpHxvRsnlXWBFCrGhf3eES3/aajLV+oceh1m4xZyUSA5mMoRvdvfmo+snVPuGPTwzz4MP1xLSgEtcQRzl1atza0Kt106HBKihKqhqJsLTSRE0xiGcZJMPpcpho/xLI+T3nmsHwQTMQD+TAgmzLBnffs6Hoart6FPQIDAQAB"
        
        # 构建完整的PEM格式公钥
        self.pem_public_key = f"""-----BEGIN PUBLIC KEY-----
{self.public_key_base64}
-----END PUBLIC KEY-----"""
        
        # 导入RSA公钥
        try:
            self.rsa_key = RSA.import_key(self.pem_public_key)
            self.cipher = PKCS1_v1_5.new(self.rsa_key)
            print("✅ RSA公钥导入成功")
        except Exception as e:
            print(f"❌ RSA公钥导入失败: {e}")
            self.rsa_key = None
            self.cipher = None

    def encrypt_password(self, password):
        """
        使用RSA公钥加密密码
        """
        if not self.cipher:
            print("❌ RSA加密器未初始化")
            return None
        
        try:
            # 将密码转换为字节
            password_bytes = password.encode('utf-8')
            
            # RSA加密
            encrypted_bytes = self.cipher.encrypt(password_bytes)
            
            # Base64编码
            encrypted_base64 = base64.b64encode(encrypted_bytes).decode('utf-8')
            
            print(f"✅ 密码加密成功: {encrypted_base64[:50]}...")
            return encrypted_base64
            
        except Exception as e:
            print(f"❌ 密码加密失败: {e}")
            return None

    def test_encryption(self):
        """
        测试加密功能
        """
        test_passwords = ["123456", "password", "test123", "18888888888"]
        
        print("=== 真正RSA加密测试 ===")
        for pwd in test_passwords:
            encrypted = self.encrypt_password(pwd)
            if encrypted:
                print(f"密码: {pwd} -> 长度: {len(encrypted)}")
            else:
                print(f"密码: {pwd} -> 加密失败")
        
        return True


def encrypt_password(password):
    """
    便捷函数：使用真正的RSA加密密码
    """
    encryptor = RealRSAEncrypt()
    return encryptor.encrypt_password(password)


def compare_with_browser_result():
    """
    与浏览器结果对比测试
    """
    print("=== 与浏览器结果对比 ===")
    
    # 测试相同的密码
    test_password = "18888888888"
    
    encryptor = RealRSAEncrypt()
    python_result = encryptor.encrypt_password(test_password)
    
    print(f"Python RSA加密结果: {python_result}")
    print(f"结果长度: {len(python_result) if python_result else 0}")
    
    # 浏览器中的结果（从您提供的截图中）
    browser_result = "fGhBK8+jW5JTJPWdV5pNR41Q3VZjpT357DOmOC+NfLB2dnQWln+q+GTTCu0NuTVKnOY4zK1G0GafG8VFjvhMVgdT7KLUapDXWJHPQCbcbmUo8eaKKMv545wSBZhVQLxAZ"
    
    print(f"浏览器加密结果: {browser_result}")
    print(f"浏览器结果长度: {len(browser_result)}")
    
    # 比较长度（RSA 1024位加密后Base64编码通常是172字符左右）
    if python_result:
        if len(python_result) == len(browser_result):
            print("✅ 长度匹配，加密算法可能正确")
        else:
            print(f"⚠️ 长度不匹配，Python: {len(python_result)}, 浏览器: {len(browser_result)}")
    
    return python_result


def main():
    """
    主测试函数
    """
    print("=== 真正的RSA加密实现测试 ===")
    
    # 基本测试
    encryptor = RealRSAEncrypt()
    if encryptor.cipher:
        encryptor.test_encryption()
        
        print("\n" + "="*50 + "\n")
        
        # 与浏览器结果对比
        compare_with_browser_result()
    else:
        print("❌ RSA加密器初始化失败")


if __name__ == "__main__":
    main()
