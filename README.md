# 东方财富网验证码加密模块

这个项目包含了东方财富网登录验证码的加密逻辑，支持 `api/captcha/get` 和 `api/captcha/Validate` 两个接口的请求参数加密。

## 文件说明

- `encrypt.js` - 核心加密模块，包含所有加密和参数构建功能
- `使用示例.py` - 完整的Python使用示例，展示如何集成到登录流程中
- `测试加密.py` - 加密函数测试脚本
- `分析.txt` - 登录流程分析文档

## 功能特性

### 核心加密功能
- **encrypt()** - 核心加密函数，使用TEA算法变种
- **base64Encode()** - Base64编码函数，兼容浏览器和Node.js环境
- **generateRequestData()** - 生成加密的请求数据
- **getTimestamp()** - 获取当前时间戳

### 接口参数构建
- **buildCaptchaGetParams()** - 构建验证码获取请求参数
- **buildCaptchaValidateParams()** - 构建验证码验证请求参数

## 使用方法

### 1. 安装依赖

```bash
pip install execjs requests ddddocr pillow
```

### 2. 基础使用

```python
import execjs

# 读取并编译JS代码
with open('encrypt.js', 'r', encoding='utf-8') as f:
    js_code = f.read()
ctx = execjs.compile(js_code)

# 构建验证码获取参数
params = ctx.call('CaptchaEncrypt.buildCaptchaGetParams',
                 'your_ctxid',
                 'your_appid', 
                 'your_account',
                 'your_password')

# 发送请求
import requests
url = "https://smartvcode2.eastmoney.com/Titan/api/captcha/get"
response = requests.get(url, params=params)
```

### 3. 完整登录流程

参考 `使用示例.py` 文件，包含：
1. 获取验证码上下文ID
2. 请求验证码
3. 识别验证码（使用ddddocr）
4. 验证验证码
5. 最终登录

## API接口说明

### api/captcha/get
获取验证码的接口，请求参数：
- `callback`: 固定值 "cb"
- `ctxid`: 验证码上下文ID
- `request`: 加密后的请求数据
- `_`: 时间戳

### api/captcha/Validate  
验证验证码的接口，请求参数：
- `callback`: 固定值 "cb"
- `ctxid`: 验证码上下文ID
- `request`: 加密后的验证数据
- `_`: 时间戳

## 加密逻辑说明

### 加密字符串格式

**验证码获取 (get):**
```
appid={appid}|ctxid={ctxid}|a={account}|p={password}|r={random}
```

**验证码验证 (validate):**
```
appid={appid}|ctxid={ctxid}|type=init|userresponse={验证码}|d={data}|a={account}|p={password}|r={random}
```

### 加密流程
1. 构建待加密字符串
2. 使用TEA算法变种进行加密
3. 对加密结果进行Base64编码
4. 添加时间戳等其他参数

## 测试

运行测试脚本验证加密功能：

```bash
python 测试加密.py
```

测试内容包括：
- 基础加密函数测试
- Base64编码测试
- 参数构建测试
- 时间戳生成测试

## 注意事项

1. **随机数影响**: 每次加密都会包含随机数，所以相同输入的加密结果会不同
2. **时间戳**: 请求参数中的 `_` 字段是时间戳，用于防重放攻击
3. **验证码类型**: 响应中的 `CaptchaType` 字段指示验证码类型
4. **环境兼容**: 代码同时支持浏览器和Node.js环境

## 登录流程

根据 `分析.txt` 中的流程分析：

1. **初次验证**: 调用 `api/captcha/get` 获取验证码
2. **响应判断**: 
   - 如果返回 "成功"，直接进入登录
   - 如果返回 "升级"，需要验证码验证
3. **验证码处理**: 下载验证码图片，使用OCR识别
4. **验证提交**: 调用 `api/captcha/Validate` 验证验证码
5. **最终登录**: 使用获得的 `validate` 值调用 `Login3` 接口

## 错误处理

常见错误及解决方案：
- **加密失败**: 检查输入参数格式
- **请求失败**: 检查网络连接和请求头
- **验证码识别失败**: 可能需要手动处理或优化OCR参数

## 更新日志

- 完善了加密模块结构
- 添加了完整的参数构建功能
- 支持两个验证码接口的加密
- 添加了时间戳生成功能
- 提供了完整的使用示例
