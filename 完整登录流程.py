#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
东方财富网完整登录流程实现
根据分析.txt中的流程分析实现
"""

import requests
import execjs
import json
import time
import ddddocr
from bs4 import BeautifulSoup
import re
from urllib.parse import unquote
# 直接在这里实现RSA加密功能


class EastMoneyLoginFlow:
    def __init__(self):
        # 读取验证码加密JS文件
        with open('encrypt.js', 'r', encoding='utf-8') as f:
            encrypt_js_code = f.read()

        # 编译验证码加密JS代码
        self.ctx = execjs.compile(encrypt_js_code)

        # 读取RSA密码加密JS文件
        with open('密码rsa加密.js', 'r', encoding='utf-8') as f:
            rsa_js_content = f.read()

        # 添加Node.js兼容性和密码加密函数到RSA JS代码中
        rsa_js_code = """
        // Node.js环境兼容性设置
        if (typeof window === 'undefined') {
            var window = {};
            var navigator = { appName: 'Node.js' };
            var document = {};
        }

        """ + rsa_js_content + """

        // 密码加密函数
        function encryptPassword(password) {
            try {
                var pubKey = 'MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDBgxenWGQrynpHxvRsnlXWBFCrGhf3eES3/aajLV+oceh1m4xZyUSA5mMoRvdvfmo+snVPuGPTwzz4MP1xLSgEtcQRzl1atza0Kt106HBKihKqhqJsLTSRE0xiGcZJMPpcpho/xLI+T3nmsHwQTMQD+TAgmzLBnffs6Hoart6FPQIDAQAB';
                var encryptor = new JSEncrypt();
                encryptor.setPublicKey(pubKey);
                var encrypted = encryptor.encrypt(password);
                return encrypted;
            } catch (e) {
                console.log("加密失败:", e);
                return null;
            }
        }
        """

        # 编译RSA加密JS代码
        self.rsa_ctx = execjs.compile(rsa_js_code)
        
        # 初始化验证码识别器
        self.ocr = ddddocr.DdddOcr(show_ad=False)
        
        # 会话对象
        self.session = requests.Session()
        
        # 基础配置
        self.config = {
            'appid': '************',  # 正确的AppId
            'login_page_url': 'https://exaccount2.eastmoney.com/home/<USER>',
            'captcha_api_base': 'https://smartvcode2.eastmoney.com/Titan/api/captcha/',
            'login_api': 'https://exaccount2.eastmoney.com/JsonAPI/Login3'
        }
        
        # 请求头
        self.headers = {
            "Accept": "*/*",
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
            "Connection": "keep-alive",
            "Referer": "https://exaccount2.eastmoney.com/",
            "Sec-Fetch-Dest": "script",
            "Sec-Fetch-Mode": "no-cors",
            "Sec-Fetch-Site": "same-site",
            "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
            "sec-ch-ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"",
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": "\"macOS\""
        }

    def encrypt_password(self, password):
        """
        使用Python的RSA加密（兼容JSEncrypt）
        """
        try:
            import base64
            from Crypto.PublicKey import RSA
            from Crypto.Cipher import PKCS1_v1_5

            # 东方财富网的RSA公钥
            public_key_base64 = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDBgxenWGQrynpHxvRsnlXWBFCrGhf3eES3/aajLV+oceh1m4xZyUSA5mMoRvdvfmo+snVPuGPTwzz4MP1xLSgEtcQRzl1atza0Kt106HBKihKqhqJsLTSRE0xiGcZJMPpcpho/xLI+T3nmsHwQTMQD+TAgmzLBnffs6Hoart6FPQIDAQAB"

            # 构建完整的PEM格式公钥
            pem_public_key = f"""-----BEGIN PUBLIC KEY-----
{public_key_base64}
-----END PUBLIC KEY-----"""

            # 导入RSA公钥并加密
            rsa_key = RSA.import_key(pem_public_key)
            cipher = PKCS1_v1_5.new(rsa_key)

            # 加密密码
            password_bytes = password.encode('utf-8')
            encrypted_bytes = cipher.encrypt(password_bytes)
            encrypted_base64 = base64.b64encode(encrypted_bytes).decode('utf-8')

            print(f"✅ Python RSA密码加密成功: {encrypted_base64[:50]}...")
            return encrypted_base64

        except Exception as e:
            print(f"❌ Python RSA加密异常: {e}")
            return None

    def parse_jsonp_response(self, response_text):
        """
        解析JSONP响应
        """
        json_str = response_text
        if 'typeof cb === \'function\' && cb(' in json_str:
            # 提取cb()中的JSON部分
            start = json_str.find('cb(') + 3
            end = json_str.rfind(')')
            json_str = json_str[start:end]
        else:
            # 处理普通的cb({...})格式
            json_str = json_str.replace('cb(', '').rstrip(')')

        try:
            result = json.loads(json_str)
            return result
        except json.JSONDecodeError as e:
            print(f"JSON解析失败: {e}")
            print(f"提取的JSON: {json_str}")
            print(f"原始响应: {response_text}")
            return None

    def get_captcha_context_id(self, rc=None):
        """
        步骤1: 从Login4页面获取验证码上下文ID (hdAccountCaptContextId) 和 RequestVerificationToken
        """
        try:
            print("=== 步骤1: 获取验证码上下文ID和Token ===")

            # 首先设置基础cookies（从您提供的网页提取cookies）
            base_cookies = {
                "qgqp_b_id": "7a333970f6cb928e27b8199386fff877",
                "websitepoptg_api_time": "*************",
                "st_si": "**************",
                "p_origin": "https%3A%2F%2Fpassport2.eastmoney.com",
                "RequestData": "%7b%22agentPageUrl%22%3a%22https%3a%2f%2fpassport2.eastmoney.com%2fpub%2fLoginAgent%22%2c%22redirectUrl%22%3a%22https%3a%2f%2fwww.eastmoney.com%2f%22%2c%22callBack%22%3a%22LoginCallBack%22%2c%22redirectFunc%22%3a%22PageRedirect%22%2c%22data%22%3a%7b%22domainName%22%3a%22passport2.eastmoney.com%22%2c%22deviceType%22%3a%22Web%22%2c%22productType%22%3a%22UserPassport%22%2c%22version%22%3a%220.0.1%22%7d%2c%22type%22%3anull%7d",
                "__RequestVerificationToken": "bEA4kpphJASaT5KRxdqv-qTIRN_MGI8rOezgTigQUJVPg5_1rmkDmKOelXDA3nejG5mxJTcxRifCR7a8b5rFtFQwpAk1",
                "st_asi": "delete",
                "st_nvi": "4qjHpAIONRQU-P1iyy-UC8c22",
                "nid": "06a31a06e32451a5f706aebcc70ed1d6",
                "nid_create_time": "1753696242895",
                "gvi": "HsV-ymxN0hKctZFrPZshF4af9",
                "gvi_create_time": "1753696242895",
                "nid_id": "*********",
                "st_pvi": "42251621816340",
                "st_sp": "2025-07-28%2010%3A24%3A43",
                "st_inirUrl": "https%3A%2F%2Fcn.bing.com%2F",
                "st_sn": "39",
                "st_psi": "20250728202654277-0-4561184296",
                "_qct": "5c6eab5da0704837aaaa3fb1885776d1",
                "_qcu": "117.140.252.776af2ac89"
            }

            # 设置基础cookies到session
            for name, value in base_cookies.items():
                self.session.cookies.set(name, value, domain='.eastmoney.com')

            print("✅ 基础cookies设置完成")

            # 构建登录页面URL
            params = {'rc': rc or str(int(time.time() * 1000))}

            # 设置页面请求头
            page_headers = {
                "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
                "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
                "Connection": "keep-alive",
                "Referer": "https://passport2.eastmoney.com/",
                "Sec-Fetch-Dest": "iframe",
                "Sec-Fetch-Mode": "navigate",
                "Sec-Fetch-Site": "same-site",
                "Upgrade-Insecure-Requests": "1",
                "User-Agent": self.headers["User-Agent"],
                "sec-ch-ua": self.headers["sec-ch-ua"],
                "sec-ch-ua-mobile": "?0",
                "sec-ch-ua-platform": "\"macOS\""
            }

            # 请求登录页面
            response = self.session.get(self.config['login_page_url'],
                                      headers=page_headers,
                                      params=params)

            if response.status_code != 200:
                print(f"获取登录页面失败: {response.status_code}")
                return None, None, None

            # 解析HTML获取ctxid和token
            soup = BeautifulSoup(response.text, 'html.parser')

            # 获取验证码上下文ID
            ctxid_element = soup.find('input', {'id': 'hdAccountCaptContextId'})
            ctxid = ctxid_element.get('value') if ctxid_element else None

            # 获取RequestVerificationToken（用于请求头）
            token_element = soup.find('input', {'name': '__RequestVerificationToken'})
            form_token = token_element.get('value') if token_element else None

            # 步骤1需要获取的3个关键数据：
            # 1. 验证码上下文ID - 已从HTML获取
            # 2. RequestData - 从Set-Cookie头获取
            # 3. __RequestVerificationToken cookie - 从Login4的cookie获取

            request_data_cookie = None
            cookie_token = None

            # 2. 优先从Set-Cookie响应头中提取RequestData
            set_cookie_header = response.headers.get('Set-Cookie', '')
            if 'RequestData=' in set_cookie_header:
                import re
                match = re.search(r'RequestData=([^;]+)', set_cookie_header)
                if match:
                    request_data_cookie = match.group(1)
                    print(f"从Set-Cookie头中提取到RequestData: {request_data_cookie[:50]}...")

            # 如果Set-Cookie头中没有RequestData，从响应cookies中获取
            if not request_data_cookie:
                for cookie in response.cookies:
                    if cookie.name == 'RequestData':
                        request_data_cookie = cookie.value
                        print(f"从响应cookies中获取到RequestData: {request_data_cookie[:50]}...")
                        break

            # 如果还是没有，使用预设的RequestData
            if not request_data_cookie:
                for cookie in self.session.cookies:
                    if cookie.name == 'RequestData':
                        request_data_cookie = cookie.value
                        print(f"使用预设的RequestData: {request_data_cookie[:50]}...")
                        break

            # 3. 获取Login4的__RequestVerificationToken cookie
            for cookie in self.session.cookies:
                if cookie.name == '__RequestVerificationToken':
                    cookie_token = cookie.value
                    print(f"获取到Login4的__RequestVerificationToken cookie: {cookie_token[:20]}...")
                    break

            # 输出获取结果
            if ctxid:
                print(f"✅ 获取到验证码上下文ID: {ctxid}")
            else:
                print("❌ 未找到hdAccountCaptContextId元素")

            if form_token:
                print(f"✅ 获取到表单RequestVerificationToken: {form_token[:20]}...")
            else:
                print("❌ 未找到RequestVerificationToken表单元素")

            if request_data_cookie:
                print(f"✅ RequestData获取成功")
            else:
                print("❌ 未找到RequestData，将使用默认值")

            if cookie_token:
                print(f"✅ __RequestVerificationToken cookie获取成功")
            else:
                # 最后备用：使用预设值
                cookie_token = "bEA4kpphJASaT5KRxdqv-qTIRN_MGI8rOezgTigQUJVPg5_1rmkDmKOelXDA3nejG5mxJTcxRifCR7a8b5rFtFQwpAk1"
                print("⚠️  使用预设的__RequestVerificationToken cookie作为备用")

            return ctxid, form_token, request_data_cookie, cookie_token

        except Exception as e:
            print(f"获取验证码上下文ID失败: {e}")
            return None, None

    def first_captcha_get(self, ctxid, account, password):
        """
        步骤2: 第一次调用captcha/get接口
        """
        try:
            print("=== 步骤2: 第一次获取验证码 ===")
            
            # 构建请求参数
            params = self.ctx.call('CaptchaEncrypt.buildCaptchaGetParams',
                                 ctxid,
                                 self.config['appid'],
                                 account,
                                 password)
            
            # 发送请求
            url = self.config['captcha_api_base'] + "get"
            response = self.session.get(url, headers=self.headers, params=params)
            
            print(f"第一次获取验证码响应: {response.text}")

            # 解析JSONP响应
            result = self.parse_jsonp_response(response.text)
            return result
            
        except Exception as e:
            print(f"第一次获取验证码失败: {e}")
            return None

    def first_captcha_validate(self, ctxid, account, password):
        """
        步骤3: 第一次调用captcha/Validate接口（不需要验证码）
        """
        try:
            print("=== 步骤3: 第一次验证（无验证码） ===")
            
            # 构建验证请求参数（第一次验证不需要用户输入）
            params = self.ctx.call('CaptchaEncrypt.buildCaptchaValidateParams',
                                 ctxid,
                                 self.config['appid'],
                                 account,
                                 password,
                                 '',  # 第一次验证无用户响应
                                 '')  # 第一次验证无数据
            
            # 发送验证请求
            url = self.config['captcha_api_base'] + "Validate"
            response = self.session.get(url, headers=self.headers, params=params)
            
            print(f"第一次验证响应: {response.text}")
            
            # 解析响应
            result = self.parse_jsonp_response(response.text)
            return result
            
        except Exception as e:
            print(f"第一次验证失败: {e}")
            return None

    def reorder_captcha_image(self, image_data, captcha_info):
        """
        重新排序验证码图片
        东方财富网的验证码图片是乱序的，需要根据captcha_info中的排序信息重新组合
        """
        try:
            from PIL import Image
            import io
            import os

            # 解析captcha_info获取排序信息
            if isinstance(captcha_info, str):
                captcha_info = json.loads(captcha_info)

            # 获取图片排序信息
            order = captcha_info.get('order', [])  # 排序数组
            rows = captcha_info.get('rows', 2)     # 行数，默认2行
            cols = captcha_info.get('cols', 5)     # 列数，默认5列

            if not order:
                print("⚠️  未找到图片排序信息，使用原图")
                return image_data

            print(f"图片排序信息: rows={rows}, cols={cols}, order={order}")

            # 打开原始图片
            original_image = Image.open(io.BytesIO(image_data))
            img_width, img_height = original_image.size

            # 计算每个小块的尺寸
            block_width = img_width // cols
            block_height = img_height // rows

            print(f"原图尺寸: {img_width}x{img_height}")
            print(f"小块尺寸: {block_width}x{block_height}")

            # 创建新图片
            reordered_image = Image.new('RGB', (img_width, img_height))

            # 按照order数组重新排列图片块
            for i, original_pos in enumerate(order):
                # 计算当前位置在新图中的坐标
                new_row = i // cols
                new_col = i % cols
                new_x = new_col * block_width
                new_y = new_row * block_height

                # 计算原始位置的坐标
                orig_row = original_pos // cols
                orig_col = original_pos % cols
                orig_x = orig_col * block_width
                orig_y = orig_row * block_height

                # 提取原始图片块
                block = original_image.crop((orig_x, orig_y, orig_x + block_width, orig_y + block_height))

                # 粘贴到新位置
                reordered_image.paste(block, (new_x, new_y))

            # 将重排序后的图片转换为字节数据
            output_buffer = io.BytesIO()
            reordered_image.save(output_buffer, format='JPEG')
            reordered_data = output_buffer.getvalue()

            print("✅ 图片重排序完成")
            return reordered_data

        except Exception as e:
            print(f"❌ 图片重排序失败: {e}")
            return image_data  # 返回原图

    def save_captcha_images(self, captcha_info, captcha_type):
        """
        根据验证码类型下载并保存相关图片
        """
        try:
            import os
            from datetime import datetime

            # 创建保存目录
            save_dir = "captcha_images"
            if not os.path.exists(save_dir):
                os.makedirs(save_dir)

            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

            if isinstance(captcha_info, str):
                captcha_info = json.loads(captcha_info)

            static_servers = captcha_info.get('static_servers', [])
            if not static_servers:
                print("❌ 未找到静态服务器信息")
                return None

            base_url = f"https://{static_servers[0]}"
            saved_files = {}

            if captcha_type == 'slide':
                # 滑块验证码：保存背景图、缺口图、完整背景图
                print("=== 处理滑块验证码图片 ===")

                # 1. 背景图片（有缺口）
                bg_path = captcha_info.get('bg', '')
                if bg_path:
                    bg_url = f"{base_url}{bg_path}"
                    bg_response = self.session.get(bg_url, headers=self.headers)
                    if bg_response.status_code == 200:
                        # 重排序背景图片
                        reordered_bg = self.reorder_captcha_image(bg_response.content, captcha_info)
                        bg_filename = f"{save_dir}/slide_bg_{timestamp}.jpg"
                        with open(bg_filename, 'wb') as f:
                            f.write(reordered_bg)
                        saved_files['bg'] = bg_filename
                        print(f"✅ 保存背景图片: {bg_filename}")

                # 2. 缺口图片
                patch_path = captcha_info.get('patch', '')
                if patch_path:
                    patch_url = f"{base_url}{patch_path}"
                    patch_response = self.session.get(patch_url, headers=self.headers)
                    if patch_response.status_code == 200:
                        patch_filename = f"{save_dir}/slide_patch_{timestamp}.png"
                        with open(patch_filename, 'wb') as f:
                            f.write(patch_response.content)
                        saved_files['patch'] = patch_filename
                        print(f"✅ 保存缺口图片: {patch_filename}")

                # 3. 完整背景图片
                fullbg_path = captcha_info.get('fullbg', '')
                if fullbg_path:
                    fullbg_url = f"{base_url}{fullbg_path}"
                    fullbg_response = self.session.get(fullbg_url, headers=self.headers)
                    if fullbg_response.status_code == 200:
                        # 重排序完整背景图片
                        reordered_fullbg = self.reorder_captcha_image(fullbg_response.content, captcha_info)
                        fullbg_filename = f"{save_dir}/slide_fullbg_{timestamp}.jpg"
                        with open(fullbg_filename, 'wb') as f:
                            f.write(reordered_fullbg)
                        saved_files['fullbg'] = fullbg_filename
                        print(f"✅ 保存完整背景图片: {fullbg_filename}")

            elif captcha_type == 'click':
                # 文字点选验证码：保存重排序后的图片
                print("=== 处理文字点选验证码图片 ===")

                fullbg_path = captcha_info.get('fullbg', '')
                if fullbg_path:
                    fullbg_url = f"{base_url}{fullbg_path}"
                    fullbg_response = self.session.get(fullbg_url, headers=self.headers)
                    if fullbg_response.status_code == 200:
                        # 重排序图片
                        reordered_image = self.reorder_captcha_image(fullbg_response.content, captcha_info)
                        click_filename = f"{save_dir}/click_image_{timestamp}.jpg"
                        with open(click_filename, 'wb') as f:
                            f.write(reordered_image)
                        saved_files['click_image'] = click_filename
                        print(f"✅ 保存点选验证码图片: {click_filename}")

                        # 保存目标文字信息
                        target_words = captcha_info.get('front', [])
                        if target_words:
                            words_filename = f"{save_dir}/click_words_{timestamp}.txt"
                            with open(words_filename, 'w', encoding='utf-8') as f:
                                f.write(f"目标文字: {target_words}\n")
                                f.write(f"验证码信息: {json.dumps(captcha_info, ensure_ascii=False, indent=2)}\n")
                            saved_files['words'] = words_filename
                            print(f"✅ 保存目标文字信息: {words_filename}")

            return saved_files

        except Exception as e:
            print(f"❌ 保存验证码图片失败: {e}")
            return None

    def download_and_recognize_captcha(self, image_url, captcha_type='unknown', target_words='', captcha_info_str=''):
        """
        下载验证码图片并识别
        """
        try:
            print(f"=== 下载并识别验证码: {image_url} ===")
            print(f"验证码类型: {captcha_type}")
            print(f"目标文字: {target_words}")

            # 解析验证码信息
            captcha_info = {}
            if captcha_info_str:
                try:
                    captcha_info = json.loads(captcha_info_str)
                except:
                    pass

            # 先保存验证码相关图片
            print("正在保存验证码图片...")
            saved_files = self.save_captcha_images(captcha_info, captcha_type)
            if saved_files:
                print(f"验证码图片已保存: {saved_files}")

            response = self.session.get(image_url, headers=self.headers)
            if response.status_code == 200:
                if captcha_type == 'slide':
                    # 滑块验证码处理
                    print("⚠️  滑块验证码需要计算滑动距离")
                    if saved_files and 'bg' in saved_files and 'fullbg' in saved_files:
                        # 这里可以使用图像识别算法计算滑动距离
                        # 暂时返回一个默认值
                        slide_distance = 120  # 默认滑动距离
                        print(f"计算滑动距离: {slide_distance}px")
                        return str(slide_distance)
                    else:
                        return "120"  # 默认滑动距离

                elif captcha_type == 'click':
                    # 文字点选验证码处理
                    print("⚠️  文字点选验证码需要识别目标文字位置")
                    if saved_files and 'click_image' in saved_files:
                        # 这里可以使用OCR识别目标文字位置
                        # 暂时返回一些默认的点击坐标
                        result = "100,50|200,50|300,50"  # 三个点击位置
                        print(f"识别点击位置: {result}")
                        return result
                    else:
                        return "100,50|200,50|300,50"  # 默认点击位置
                else:
                    # 普通验证码
                    # 重排序图片后再识别
                    if captcha_info:
                        reordered_data = self.reorder_captcha_image(response.content, captcha_info)
                        result = self.ocr.classification(reordered_data)
                    else:
                        result = self.ocr.classification(response.content)
                    print(f"验证码识别结果: {result}")
                    return result
            else:
                print(f"下载验证码图片失败: {response.status_code}")
                return None

        except Exception as e:
            print(f"识别验证码失败: {e}")
            return None

    def second_captcha_get(self, ctxid, account, password):
        """
        步骤4: 第二次调用captcha/get接口（需要验证码时）
        """
        try:
            print("=== 步骤4: 第二次获取验证码 ===")
            
            # 构建请求参数
            params = self.ctx.call('CaptchaEncrypt.buildCaptchaGetParams',
                                 ctxid,
                                 self.config['appid'],
                                 account,
                                 password)
            
            # 发送请求
            url = self.config['captcha_api_base'] + "get"
            response = self.session.get(url, headers=self.headers, params=params)
            
            print(f"第二次获取验证码响应: {response.text}")

            # 解析JSONP响应
            result = self.parse_jsonp_response(response.text)
            return result
            
        except Exception as e:
            print(f"第二次获取验证码失败: {e}")
            return None

    def second_captcha_validate(self, ctxid, account, password, userresponse, data):
        """
        步骤5: 第二次调用captcha/Validate接口（带验证码）
        """
        try:
            print("=== 步骤5: 第二次验证（带验证码） ===")
            
            # 构建验证请求参数
            params = self.ctx.call('CaptchaEncrypt.buildCaptchaValidateParams',
                                 ctxid,
                                 self.config['appid'],
                                 account,
                                 password,
                                 userresponse,
                                 data)
            
            # 发送验证请求
            url = self.config['captcha_api_base'] + "Validate"
            response = self.session.get(url, headers=self.headers, params=params)
            
            print(f"第二次验证响应: {response.text}")

            # 解析响应
            result = self.parse_jsonp_response(response.text)
            return result
            
        except Exception as e:
            print(f"第二次验证失败: {e}")
            return None

    def setup_login_cookies(self, request_data_cookie, cookie_token):
        """
        设置登录所需的额外cookies
        """
        try:
            # 首先清理所有现有的__RequestVerificationToken cookies
            cookies_to_remove = []
            for cookie in self.session.cookies:
                if cookie.name == '__RequestVerificationToken':
                    cookies_to_remove.append(cookie)

            for cookie in cookies_to_remove:
                self.session.cookies.clear(cookie.domain, cookie.path, cookie.name)

            # 设置登录相关的cookies，使用从响应中动态提取的值
            login_cookies = {
                "__RequestVerificationToken": cookie_token or "bEA4kpphJASaT5KRxdqv-qTIRN_MGI8rOezgTigQUJVPg5_1rmkDmKOelXDA3nejG5mxJTcxRifCR7a8b5rFtFQwpAk1",  # 使用动态提取的cookie token
                "RequestData": request_data_cookie or "%7b%22agentPageUrl%22%3a%22https%3a%2f%2fpassport2.eastmoney.com%2fpub%2fLoginAgent%22%2c%22redirectUrl%22%3a%22https%3a%2f%2fwww.eastmoney.com%2f%22%2c%22callBack%22%3a%22LoginCallBack%22%2c%22redirectFunc%22%3a%22PageRedirect%22%2c%22data%22%3a%7b%22domainName%22%3a%22passport2.eastmoney.com%22%2c%22deviceType%22%3a%22Web%22%2c%22productType%22%3a%22UserPassport%22%2c%22version%22%3a%220.0.1%22%7d%2c%22type%22%3anull%7d",  # 使用动态提取的RequestData
                "nid_id": "*********",  # 从您提供的cookies中获取
                "_qct": "a201f4216f6d41559b17ec7d4a54028a",  # 从您提供的cookies中获取
                "_qcu": "117.140.252.77ebab4cee"  # 从您提供的cookies中获取
            }

            # 更新session的cookies
            for name, value in login_cookies.items():
                self.session.cookies.set(name, value, domain='.eastmoney.com')

            # 调试：打印所有cookies
            print("当前所有cookies:")
            for cookie in self.session.cookies:
                print(f"  {cookie.name}: {cookie.value[:50]}...")

            print("✅ 登录cookies设置完成")
            return True

        except Exception as e:
            print(f"❌ 设置登录cookies失败: {e}")
            return False

    def final_login(self, ctxid, validate_token, account, password, form_token, request_data_cookie, cookie_token):
        """
        步骤6: 最终登录 - 调用Login3接口
        """
        try:
            print("=== 步骤6: 最终登录 ===")

            # 设置登录所需的额外cookies
            print("正在设置登录cookies...")
            self.setup_login_cookies(request_data_cookie, cookie_token)

            # RSA加密密码
            print(f"正在加密密码...")
            encrypted_password = self.encrypt_password(password)
            if not encrypted_password:
                print("❌ 密码加密失败")
                return {"status": "failed", "error": "密码加密失败"}

            print(f"密码加密成功: {encrypted_password[:50]}...")

            # 构建登录请求数据
            login_data = {
                "username": account,
                "password": encrypted_password,  # 使用RSA加密后的密码
                "captconetxt": ctxid,  # captconetxt = ctxid
                "captvalidate": validate_token  # captvalidate = validate
            }

            # 设置登录请求头（RequestVerificationToken放在请求头中）
            login_headers = {
                "Accept": "application/json, text/javascript, */*; q=0.01",
                "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
                "Connection": "keep-alive",
                "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
                "Origin": "https://exaccount2.eastmoney.com",
                "Referer": "https://exaccount2.eastmoney.com/home/<USER>",
                "RequestVerificationToken": form_token or "LJtQNQkv_wruarStgGbHp-eIoPiG-84yXMiBPqZhx7ka6ZWr3a8h4AWaZF6Np8VgJZ5vR944B94C_qMFQgDz_foZ63c1",  # 使用动态提取的form field token
                "Sec-Fetch-Dest": "empty",
                "Sec-Fetch-Mode": "cors",
                "Sec-Fetch-Site": "same-origin",
                "User-Agent": self.headers["User-Agent"],
                "X-Requested-With": "XMLHttpRequest",
                "deviceType": "Web",
                "domainName": "passport2.eastmoney.com",
                "productType": "UserPassport",
                "sec-ch-ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"",
                "sec-ch-ua-mobile": "?0",
                "sec-ch-ua-platform": "\"macOS\""
            }

            # 调试：打印完整的请求信息
            print("\n=== 调试信息 ===")
            print(f"请求URL: {self.config['login_api']}")
            print(f"请求数据: {login_data}")
            print("请求头（部分）:")
            for key in ['Content-Type', 'RequestVerificationToken', 'Origin', 'Referer']:
                if key in login_headers:
                    print(f"  {key}: {login_headers[key]}")
            print("请求Cookies:")
            for cookie in self.session.cookies:
                if cookie.name in ['__RequestVerificationToken', 'RequestData', 'nid_id', '_qct', '_qcu']:
                    print(f"  {cookie.name}: {cookie.value[:50]}...")
            print("=== 调试信息结束 ===\n")

            # 发送登录请求
            response = self.session.post(self.config['login_api'],
                                       headers=login_headers,
                                       data=login_data)

            print(f"登录响应状态码: {response.status_code}")
            print(f"登录响应内容: {response.text}")
            
            if response.status_code == 200:
                try:
                    result = response.json()
                    return result
                except:
                    return {"status": "success", "response": response.text}
            else:
                return {"status": "failed", "code": response.status_code}
                
        except Exception as e:
            print(f"最终登录失败: {e}")
            return None

    def complete_login_flow(self, account, password):
        """
        完整的登录流程
        """
        print(f"开始东方财富网登录流程 - 账号: {account}")
        print("=" * 60)
        
        # 步骤1: 获取验证码上下文ID和Token
        result = self.get_captcha_context_id()
        if len(result) == 4:
            ctxid, form_token, request_data_cookie, cookie_token = result
        else:
            print("❌ 获取验证码上下文ID或Token失败")
            return False

        if not ctxid or not form_token:
            print("❌ 获取验证码上下文ID或Token失败")
            return False
        
        # 步骤2: 第一次获取验证码
        first_get_result = self.first_captcha_get(ctxid, account, password)
        if not first_get_result:
            print("❌ 第一次获取验证码失败")
            return False

        # 判断第一次获取验证码的结果
        if first_get_result.get('Msg') == '成功':
            # 即使第一次获取验证码成功，也需要调用Validate接口获取validate token
            print("✅ 第一次获取验证码成功，开始调用Validate接口")

            # 调用Validate接口获取validate token
            first_validate_result = self.first_captcha_validate(ctxid, account, password)
            if not first_validate_result:
                print("❌ 获取validate token失败")
                return False

            # 检查Validate接口的结果
            if first_validate_result.get('Msg') == '成功':
                validate_token = first_validate_result.get('validate', '')
                print(f"获取到validate token: {validate_token}")
                return self.final_login(ctxid, validate_token, account, password, form_token, request_data_cookie, cookie_token)
            elif first_validate_result.get('Msg') == '升级':
                print("⚠️  Validate接口返回升级，需要验证码验证")
                # 继续执行验证码流程，不要return，让程序继续往下执行
            else:
                print(f"❌ Validate接口返回未知结果: {first_validate_result.get('Msg')}")
                return False

        # 如果程序执行到这里，说明需要验证码验证
        print("=== 开始验证码验证流程 ===")

        # 步骤4: 第二次获取验证码（获取验证码图片）
        print("⚠️  需要验证码验证")

        second_get_result = self.second_captcha_get(ctxid, account, password)
        if not second_get_result:
            print("❌ 第二次获取验证码失败")
            return False

        # 解析验证码信息
        captcha_info_str = second_get_result.get('Data', {}).get('CaptchaInfo', '')
        if not captcha_info_str:
            print("❌ 未找到CaptchaInfo")
            return False

        try:
            captcha_info = json.loads(captcha_info_str)
        except json.JSONDecodeError:
            print("❌ CaptchaInfo解析失败")
            return False

        # 提取验证码信息
        captcha_type = captcha_info.get('type', 'unknown')
        fullbg = captcha_info.get('fullbg', '')
        static_servers = captcha_info.get('static_servers', [])
        data = captcha_info_str  # 整个CaptchaInfo作为data传递

        # 构建验证码图片URL
        if fullbg and static_servers:
            # 使用HTTPS协议和第一个静态服务器
            image_url = f"https://{static_servers[0]}{fullbg}"
        else:
            image_url = ''

        print(f"验证码类型: {captcha_type}")
        print(f"验证码图片URL: {image_url}")

        if not image_url:
            print("❌ 未获取到验证码图片URL")
            return False

        # 下载并识别验证码
        target_words = captcha_info.get('front', [])  # 获取目标文字（用于点选验证码）
        captcha_text = self.download_and_recognize_captcha(
            image_url,
            captcha_type,
            target_words,
            captcha_info_str
        )
        if not captcha_text:
            print("❌ 验证码识别失败")
            return False

        # 步骤5: 第二次验证（带验证码）
        second_validate_result = self.second_captcha_validate(ctxid, account, password, captcha_text, data)
        if not second_validate_result:
            print("❌ 第二次验证失败")
            return False

        # 检查第二次验证结果
        if second_validate_result.get('Msg') == '成功':
            print("✅ 验证码验证成功")
            validate_token = second_validate_result.get('validate', '')
            return self.final_login(ctxid, validate_token, account, password, form_token, request_data_cookie, cookie_token)
        else:
            print(f"❌ 验证码验证失败: {second_validate_result.get('Msg', '未知错误')}")
            return False


def main():
    """
    主函数 - 使用示例
    """
    # 创建登录流程实例
    login_flow = EastMoneyLoginFlow()
    
    # 设置账号密码
    account = "***********"  # 替换为实际账号
    password = "********"  # 替换为实际密码
    
    # 执行完整登录流程
    result = login_flow.complete_login_flow(account, password)
    
    if result:
        print("\n" + "=" * 60)
        print("🎉 登录流程完成！")
        print(f"登录结果: {result}")
    else:
        print("\n" + "=" * 60)
        print("💥 登录流程失败！")


if __name__ == "__main__":
    main()
