#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
东方财富网验证码处理完整示例
演示如何处理乱序验证码图片的重排序和保存
"""

import json
import os
from 完整登录流程 import EastMoneyLoginFlow

def demo_captcha_processing():
    """
    演示验证码处理流程
    """
    print("🎯 东方财富网验证码处理演示")
    print("=" * 60)
    
    # 创建登录流程实例
    login_flow = EastMoneyLoginFlow()
    
    print("📋 验证码类型说明:")
    print("1. slide (滑块验证码) - 需要计算滑动距离")
    print("2. click (文字点选验证码) - 需要识别目标文字位置")
    print("3. 所有验证码图片都是乱序的，需要重新排序")
    print()
    
    # 示例1: 滑块验证码处理
    print("🔧 示例1: 滑块验证码处理")
    print("-" * 40)
    
    slide_captcha_info = {
        "type": "slide",
        "static_servers": ["smartvcode2.eastmoney.com"],
        "bg": "/09/resources/e02b_160/3/6c/bg.jpg",           # 有缺口的背景图
        "patch": "/09/resources/e02b_160/3/6c/patch.png",     # 缺口图片
        "fullbg": "/09/resources/e02b_160/3/6c/fullbg.jpg",   # 完整背景图
        "order": [4, 1, 8, 2, 6, 0, 7, 3, 9, 5],            # 图片块的乱序排列
        "rows": 2,                                            # 图片分为2行
        "cols": 5                                             # 图片分为5列
    }
    
    print(f"验证码类型: {slide_captcha_info['type']}")
    print(f"图片规格: {slide_captcha_info['rows']}行 x {slide_captcha_info['cols']}列")
    print(f"乱序排列: {slide_captcha_info['order']}")
    print("处理步骤:")
    print("  1. 下载背景图片（有缺口）并重排序")
    print("  2. 下载缺口图片（不需要重排序）")
    print("  3. 下载完整背景图片并重排序")
    print("  4. 通过对比完整图和缺口图计算滑动距离")
    print()
    
    # 示例2: 文字点选验证码处理
    print("🔧 示例2: 文字点选验证码处理")
    print("-" * 40)
    
    click_captcha_info = {
        "type": "click",
        "static_servers": ["smartvcode2.eastmoney.com"],
        "fullbg": "/09/resources/e02b_160/3/6c/click.jpg",    # 验证码图片
        "front": ["点击", "文字", "验证"],                      # 需要点击的目标文字
        "order": [7, 2, 9, 1, 5, 3, 8, 0, 6, 4],            # 图片块的乱序排列
        "rows": 2,                                            # 图片分为2行
        "cols": 5                                             # 图片分为5列
    }
    
    print(f"验证码类型: {click_captcha_info['type']}")
    print(f"目标文字: {click_captcha_info['front']}")
    print(f"图片规格: {click_captcha_info['rows']}行 x {click_captcha_info['cols']}列")
    print(f"乱序排列: {click_captcha_info['order']}")
    print("处理步骤:")
    print("  1. 下载验证码图片并重排序")
    print("  2. 使用OCR识别图片中的所有文字")
    print("  3. 找到目标文字的位置坐标")
    print("  4. 返回点击坐标（格式: x1,y1|x2,y2|x3,y3）")
    print()
    
    # 示例3: 图片重排序算法说明
    print("🔧 示例3: 图片重排序算法")
    print("-" * 40)
    
    print("原理说明:")
    print("1. 东方财富网的验证码图片被分割成小块并打乱顺序")
    print("2. order数组记录了每个位置应该放置哪个原始块")
    print("3. 重排序算法将乱序的块按正确顺序重新组合")
    print()
    
    print("算法步骤:")
    print("  原始图片: 300x150 像素，分为2行5列")
    print("  每个小块: 60x75 像素")
    print("  order=[4,1,8,2,6,0,7,3,9,5] 表示:")
    print("    位置0放原始块4，位置1放原始块1，...")
    print("    位置9放原始块5")
    print()
    
    # 示例4: 文件保存结构
    print("🔧 示例4: 文件保存结构")
    print("-" * 40)
    
    print("保存目录: captcha_images/")
    print("滑块验证码文件:")
    print("  - slide_bg_20250728_143022.jpg     (重排序后的背景图)")
    print("  - slide_patch_20250728_143022.png  (缺口图片)")
    print("  - slide_fullbg_20250728_143022.jpg (重排序后的完整背景图)")
    print()
    print("文字点选验证码文件:")
    print("  - click_image_20250728_143022.jpg  (重排序后的验证码图片)")
    print("  - click_words_20250728_143022.txt  (目标文字信息)")
    print()
    
    # 示例5: 使用方法
    print("🔧 示例5: 代码使用方法")
    print("-" * 40)
    
    code_example = '''
# 1. 创建登录流程实例
login_flow = EastMoneyLoginFlow()

# 2. 调用验证码识别函数
result = login_flow.download_and_recognize_captcha(
    image_url="https://smartvcode2.eastmoney.com/path/to/image.jpg",
    captcha_type="slide",  # 或 "click"
    target_words=["目标", "文字"],  # 仅用于点选验证码
    captcha_info_str=json.dumps(captcha_info)  # 验证码信息JSON
)

# 3. 处理识别结果
if captcha_type == "slide":
    slide_distance = int(result)  # 滑动距离（像素）
elif captcha_type == "click":
    coordinates = result.split("|")  # 点击坐标列表
'''
    
    print(code_example)
    
    print("=" * 60)
    print("✅ 验证码处理演示完成！")
    print()
    print("🎉 主要功能:")
    print("✓ 自动识别验证码类型")
    print("✓ 图片重排序算法")
    print("✓ 滑块验证码处理")
    print("✓ 文字点选验证码处理")
    print("✓ 自动保存处理后的图片")
    print("✓ 集成到完整登录流程")

if __name__ == "__main__":
    demo_captcha_processing()
