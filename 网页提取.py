import requests
from bs4 import BeautifulSoup

headers = {
    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
    "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
    "Connection": "keep-alive",
    "Referer": "https://passport2.eastmoney.com/",
    "Sec-Fetch-Dest": "iframe",
    "Sec-Fetch-Mode": "navigate",
    "Sec-Fetch-Site": "same-site",
    "Upgrade-Insecure-Requests": "1",
    "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0",
    "sec-ch-ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"",
    "sec-ch-ua-mobile": "?0",
    "sec-ch-ua-platform": "\"macOS\""
}
cookies = {
    "qgqp_b_id": "7a333970f6cb928e27b8199386fff877",
    "websitepoptg_api_time": "1753669483421",
    "st_si": "88721724408909",
    "p_origin": "https%3A%2F%2Fpassport2.eastmoney.com",
    "RequestData": "%7b%22agentPageUrl%22%3a%22https%3a%2f%2fpassport2.eastmoney.com%2fpub%2fLoginAgent%22%2c%22redirectUrl%22%3a%22https%3a%2f%2fwww.eastmoney.com%2f%22%2c%22callBack%22%3a%22LoginCallBack%22%2c%22redirectFunc%22%3a%22PageRedirect%22%2c%22data%22%3a%7b%22domainName%22%3a%22passport2.eastmoney.com%22%2c%22deviceType%22%3a%22Web%22%2c%22productType%22%3a%22UserPassport%22%2c%22version%22%3a%220.0.1%22%7d%2c%22type%22%3anull%7d",
    "__RequestVerificationToken": "bEA4kpphJASaT5KRxdqv-qTIRN_MGI8rOezgTigQUJVPg5_1rmkDmKOelXDA3nejG5mxJTcxRifCR7a8b5rFtFQwpAk1",
    "st_pvi": "**************",
    "st_sp": "2025-07-28%2010%3A24%3A43",
    "st_inirUrl": "https%3A%2F%2Fcn.bing.com%2F",
    "st_sn": "2",
    "st_psi": "*****************-0-**********",
    "_qct": "3ef9272c39d14322bfa1fff5122b94af",
    "_qcu": "117.140.252.774b26f6e8",
    "st_asi": "*****************-0-**********"
}
url = "https://exaccount2.eastmoney.com/home/<USER>"
params = {
    "rc": "*********"
}

response = requests.get(url, headers=headers, cookies=cookies, params=params)

# Parse the HTML content
soup = BeautifulSoup(response.text, 'html.parser')

# Find the input element with id="hdMobCaptContextId"
input_element = soup.find('input', {'id': 'hdAccountCaptContextId'})

# Extract the value attribute
if input_element:
    value = input_element.get('value')
    print(value)
else:
    print("Input element not found")