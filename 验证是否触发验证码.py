import requests


headers = {
    "Accept": "*/*",
    "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
    "Connection": "keep-alive",
    "Referer": "https://exaccount2.eastmoney.com/",
    "Sec-Fetch-Dest": "script",
    "Sec-Fetch-Mode": "no-cors",
    "Sec-Fetch-Site": "same-site",
    "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
    "sec-ch-ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"",
    "sec-ch-ua-mobile": "?0",
    "sec-ch-ua-platform": "\"macOS\""
}
cookies = {
    "qgqp_b_id": "7a333970f6cb928e27b8199386fff877",
    "websitepoptg_api_time": "*************",
    "st_si": "**************",
    "p_origin": "https%3A%2F%2Fpassport2.eastmoney.com",
    "st_asi": "delete",
    "st_pvi": "**************",
    "st_sp": "2025-07-28%2010%3A24%3A43",
    "st_inirUrl": "https%3A%2F%2Fcn.bing.com%2F",
    "st_sn": "13",
    "st_psi": "*****************-************-**********"
}
url = "https://smartvcode2.eastmoney.com/Titan/api/captcha/Validate"
params = {
    "callback": "cb",
    "ctxid": "d6ac405cb3cd727ee64fc00b1f747429",
    "request": "V1MXn3L8kqlFUn2t1fxO1US/VkjRsSLMNXL8CamaxpCgbq4DqpFyIdhGts+570Hx8aLMJCdVxq0wh2hqQZm7CqIBtuJA926BpkzcdLE+xJufViPPs8icY0SR8vQ7jpPnrUGVgzoB3Zygz5w8CGRBUILYzqrUdEIP92Xucd42FkR0FD+CEwQDTwc0B70/29JwLnl9DKlWO1+kqZWrSRGS+oDnQnN6FZtdMDnEbR+PPZkTew9fIUAynBg0ltr/Qf0Kh4KroRQP1gKJETk/6VzEmKX+nNf6meKTmcVuMk4maDicpl6TFz4yoeZ52JJq3WdyDfacoSxsQzM9QqdKHdNfaH6uWho09bIV+AChJHyipBQmZvqh3NuKyyfOEm6I1o63HyrIzJq6FIy5l4RxePXnQmM1LM7EV9+QzKrI5w==",
    "_": "1753688345789"
}
response = requests.get(url, headers=headers, cookies=cookies, params=params)

print(response.text)
print(response)