#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
兼容JSEncrypt的RSA加密实现
尝试匹配浏览器中JSEncrypt的行为
"""

import base64
from Crypto.PublicKey import RSA
from Crypto.Cipher import PKCS1_v1_5
import binascii
import os


class JSEncryptCompatible:
    def __init__(self):
        # 东方财富网的RSA公钥（Base64格式）
        self.public_key_base64 = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDBgxenWGQrynpHxvRsnlXWBFCrGhf3eES3/aajLV+oceh1m4xZyUSA5mMoRvdvfmo+snVPuGPTwzz4MP1xLSgEtcQRzl1atza0Kt106HBKihKqhqJsLTSRE0xiGcZJMPpcpho/xLI+T3nmsHwQTMQD+TAgmzLBnffs6Hoart6FPQIDAQAB"
        
        # 构建完整的PEM格式公钥
        self.pem_public_key = f"""-----BEGIN PUBLIC KEY-----
{self.public_key_base64}
-----END PUBLIC KEY-----"""
        
        # 导入RSA公钥
        try:
            self.rsa_key = RSA.import_key(self.pem_public_key)
            self.cipher = PKCS1_v1_5.new(self.rsa_key)
            print("✅ JSEncrypt兼容RSA公钥导入成功")
        except Exception as e:
            print(f"❌ RSA公钥导入失败: {e}")
            self.rsa_key = None
            self.cipher = None

    def encrypt_password_method1(self, password):
        """
        方法1：标准PKCS#1 v1.5填充
        """
        if not self.cipher:
            return None
        
        try:
            password_bytes = password.encode('utf-8')
            encrypted_bytes = self.cipher.encrypt(password_bytes)
            encrypted_base64 = base64.b64encode(encrypted_bytes).decode('utf-8')
            return encrypted_base64
        except Exception as e:
            print(f"方法1加密失败: {e}")
            return None

    def encrypt_password_method2(self, password):
        """
        方法2：尝试不同的编码方式
        """
        if not self.cipher:
            return None
        
        try:
            password_bytes = password.encode('utf-8')
            encrypted_bytes = self.cipher.encrypt(password_bytes)
            
            # 尝试去掉Base64的填充字符
            encrypted_base64 = base64.b64encode(encrypted_bytes).decode('utf-8').rstrip('=')
            return encrypted_base64
        except Exception as e:
            print(f"方法2加密失败: {e}")
            return None

    def encrypt_password_method3(self, password):
        """
        方法3：使用原始的RSA加密（不使用PKCS#1填充）
        """
        try:
            # 手动实现RSA加密
            password_bytes = password.encode('utf-8')
            
            # 将密码转换为整数
            password_int = int.from_bytes(password_bytes, 'big')
            
            # RSA加密：c = m^e mod n
            encrypted_int = pow(password_int, self.rsa_key.e, self.rsa_key.n)
            
            # 转换为字节
            key_size = (self.rsa_key.size_in_bits() + 7) // 8
            encrypted_bytes = encrypted_int.to_bytes(key_size, 'big')
            
            # Base64编码
            encrypted_base64 = base64.b64encode(encrypted_bytes).decode('utf-8')
            return encrypted_base64
        except Exception as e:
            print(f"方法3加密失败: {e}")
            return None

    def encrypt_password_method4(self, password):
        """
        方法4：模拟JSEncrypt的具体实现
        """
        try:
            # JSEncrypt可能使用了特定的填充方式
            password_bytes = password.encode('utf-8')
            
            # 手动添加PKCS#1 v1.5填充
            key_size = (self.rsa_key.size_in_bits() + 7) // 8  # 128 bytes for 1024-bit key
            padding_length = key_size - len(password_bytes) - 3
            
            if padding_length < 8:
                raise ValueError("密码太长，无法加密")
            
            # 构造填充：0x00 || 0x02 || random_bytes || 0x00 || message
            padded_message = bytearray([0x00, 0x02])
            
            # 添加随机填充字节（非零）
            for _ in range(padding_length):
                padded_message.append(os.urandom(1)[0] or 1)  # 确保非零
            
            padded_message.append(0x00)
            padded_message.extend(password_bytes)
            
            # 转换为整数并加密
            message_int = int.from_bytes(padded_message, 'big')
            encrypted_int = pow(message_int, self.rsa_key.e, self.rsa_key.n)
            
            # 转换为字节并Base64编码
            encrypted_bytes = encrypted_int.to_bytes(key_size, 'big')
            encrypted_base64 = base64.b64encode(encrypted_bytes).decode('utf-8')
            
            return encrypted_base64
        except Exception as e:
            print(f"方法4加密失败: {e}")
            return None

    def test_all_methods(self, password="18888888888"):
        """
        测试所有加密方法
        """
        print(f"=== 测试密码: {password} ===")
        
        methods = [
            ("方法1 (标准PKCS#1)", self.encrypt_password_method1),
            ("方法2 (去填充)", self.encrypt_password_method2),
            ("方法3 (原始RSA)", self.encrypt_password_method3),
            ("方法4 (手动填充)", self.encrypt_password_method4),
        ]
        
        results = {}
        
        for method_name, method_func in methods:
            result = method_func(password)
            if result:
                results[method_name] = result
                print(f"{method_name}: {result[:50]}... (长度: {len(result)})")
            else:
                print(f"{method_name}: 失败")
        
        # 浏览器结果（从您的截图）
        browser_result = "fGhBK8+jW5JTJPWdV5pNR41Q3VZjpT357DOmOC+NfLB2dnQWln+q+GTTCu0NuTVKnOY4zK1G0GafG8VFjvhMVgdT7KLUapDXWJHPQCbcbmUo8eaKKMv545wSBZhVQLxAZ"
        print(f"浏览器结果: {browser_result[:50]}... (长度: {len(browser_result)})")
        
        # 找到最接近的方法
        browser_length = len(browser_result)
        closest_method = None
        min_diff = float('inf')
        
        for method_name, result in results.items():
            diff = abs(len(result) - browser_length)
            if diff < min_diff:
                min_diff = diff
                closest_method = method_name
        
        if closest_method:
            print(f"✅ 最接近浏览器结果的方法: {closest_method}")
            return results[closest_method]
        
        return None

    def encrypt_password(self, password):
        """
        主加密函数，使用最佳方法
        """
        # 先尝试方法2（去填充），因为长度更接近浏览器结果
        result = self.encrypt_password_method2(password)
        if result:
            return result
        
        # 如果失败，使用标准方法
        return self.encrypt_password_method1(password)


def encrypt_password(password):
    """
    便捷函数：使用JSEncrypt兼容的方式加密密码
    """
    encryptor = JSEncryptCompatible()
    return encryptor.encrypt_password(password)


def main():
    """
    测试函数
    """
    print("=== JSEncrypt兼容性测试 ===")
    
    encryptor = JSEncryptCompatible()
    if encryptor.cipher:
        # 测试所有方法
        best_result = encryptor.test_all_methods("18888888888")
        
        if best_result:
            print(f"\n✅ 推荐使用结果: {best_result}")
        else:
            print("\n❌ 所有方法都失败了")
    else:
        print("❌ RSA加密器初始化失败")


if __name__ == "__main__":
    main()
