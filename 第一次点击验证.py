import requests


headers = {
    "Accept": "*/*",
    "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
    "Connection": "keep-alive",
    "Referer": "https://exaccount2.eastmoney.com/",
    "Sec-Fetch-Dest": "script",
    "Sec-Fetch-Mode": "no-cors",
    "Sec-Fetch-Site": "same-site",
    "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
    "sec-ch-ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"",
    "sec-ch-ua-mobile": "?0",
    "sec-ch-ua-platform": "\"macOS\""
}
cookies = {
    "qgqp_b_id": "7a333970f6cb928e27b8199386fff877",
    "websitepoptg_api_time": "*************",
    "st_si": "**************",
    "p_origin": "https%3A%2F%2Fpassport2.eastmoney.com",
    "st_asi": "delete",
    "st_pvi": "**************",
    "st_sp": "2025-07-28%2010%3A24%3A43",
    "st_inirUrl": "https%3A%2F%2Fcn.bing.com%2F",
    "st_sn": "13",
    "st_psi": "*****************-************-**********"
}
url = "https://smartvcode2.eastmoney.com/Titan/api/captcha/get"
params = {
    "callback": "cb",
    "ctxid": "98156d95be78f8855f058463b0be37d3",
    "request": "ZKk+3tHjraXNmZ2vaCtKwLuCClOBe08xbBK6ImY5WBzfnKrnOymdKu2x073g+gHq5uqCpSH+WQYkr6o51llrt9xkVKTQhLGLJo2Rk0Rk6aJ0vVNpU1suhIOmtitbFZsv3I6eJPQZMBES1lMdASiOkg==",
    "_": "1753688345786"
}
response = requests.get(url, headers=headers, cookies=cookies, params=params)

print(response.text)
print(response)