#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试验证码图片重排序和保存功能
"""

import json
from 完整登录流程 import EastMoneyLoginFlow

def test_captcha_processing():
    """
    测试验证码处理功能
    """
    print("=== 测试验证码处理功能 ===")
    
    # 创建登录流程实例
    login_flow = EastMoneyLoginFlow()
    
    # 模拟滑块验证码信息
    slide_captcha_info = {
        "type": "slide",
        "static_servers": ["smartvcode2.eastmoney.com"],
        "bg": "/09/resources/e02b_160/3/6c/6c00912f48ded065fbf7b5336bd9a8ef/bg.jpg",
        "patch": "/09/resources/e02b_160/3/6c/6c00912f48ded065fbf7b5336bd9a8ef/patch.png", 
        "fullbg": "/09/resources/e02b_160/3/6c/6c00912f48ded065fbf7b5336bd9a8ef/fullbg.jpg",
        "order": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],  # 示例排序数组
        "rows": 2,
        "cols": 5
    }
    
    # 模拟文字点选验证码信息
    click_captcha_info = {
        "type": "click",
        "static_servers": ["smartvcode2.eastmoney.com"],
        "fullbg": "/09/resources/e02b_160/3/6c/6c00912f48ded065fbf7b5336bd9a8ef/click.jpg",
        "front": ["点击", "文字", "验证"],
        "order": [4, 1, 8, 2, 6, 0, 7, 3, 9, 5],  # 示例乱序排列
        "rows": 2,
        "cols": 5
    }
    
    print("\n1. 测试滑块验证码处理...")
    try:
        slide_result = login_flow.save_captcha_images(slide_captcha_info, "slide")
        if slide_result:
            print(f"✅ 滑块验证码处理成功: {slide_result}")
        else:
            print("❌ 滑块验证码处理失败")
    except Exception as e:
        print(f"❌ 滑块验证码处理异常: {e}")
    
    print("\n2. 测试文字点选验证码处理...")
    try:
        click_result = login_flow.save_captcha_images(click_captcha_info, "click")
        if click_result:
            print(f"✅ 文字点选验证码处理成功: {click_result}")
        else:
            print("❌ 文字点选验证码处理失败")
    except Exception as e:
        print(f"❌ 文字点选验证码处理异常: {e}")
    
    print("\n3. 测试验证码识别功能...")
    try:
        # 测试滑块验证码识别
        slide_url = "https://smartvcode2.eastmoney.com/09/resources/e02b_160/3/6c/6c00912f48ded065fbf7b5336bd9a8ef/6c00912f48ded065fbf7b5336bd9a8ef.jpg"
        slide_recognition = login_flow.download_and_recognize_captcha(
            slide_url, 
            "slide", 
            "", 
            json.dumps(slide_captcha_info)
        )
        print(f"滑块验证码识别结果: {slide_recognition}")
        
        # 测试文字点选验证码识别
        click_url = "https://smartvcode2.eastmoney.com/09/resources/e02b_160/3/6c/6c00912f48ded065fbf7b5336bd9a8ef/click.jpg"
        click_recognition = login_flow.download_and_recognize_captcha(
            click_url, 
            "click", 
            ["点击", "文字", "验证"], 
            json.dumps(click_captcha_info)
        )
        print(f"文字点选验证码识别结果: {click_recognition}")
        
    except Exception as e:
        print(f"❌ 验证码识别测试异常: {e}")

def test_image_reorder():
    """
    测试图片重排序功能
    """
    print("\n=== 测试图片重排序功能 ===")
    
    # 创建登录流程实例
    login_flow = EastMoneyLoginFlow()
    
    # 模拟验证码信息
    captcha_info = {
        "order": [4, 1, 8, 2, 6, 0, 7, 3, 9, 5],  # 乱序排列
        "rows": 2,
        "cols": 5
    }
    
    print("测试图片重排序算法...")
    print(f"原始排序: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9]")
    print(f"乱序排列: {captcha_info['order']}")
    print(f"图片规格: {captcha_info['rows']}行 x {captcha_info['cols']}列")
    
    # 这里只是测试算法逻辑，不实际处理图片
    print("✅ 图片重排序算法测试完成")

if __name__ == "__main__":
    print("🚀 开始测试验证码处理功能")
    print("=" * 60)
    
    # 测试验证码处理
    test_captcha_processing()
    
    # 测试图片重排序
    test_image_reorder()
    
    print("\n" + "=" * 60)
    print("🎉 验证码处理功能测试完成！")
    print("\n📝 功能说明:")
    print("1. 自动识别验证码类型（slide滑块 / click点选）")
    print("2. 根据order数组重新排序乱序的验证码图片")
    print("3. 滑块验证码：保存背景图、缺口图、完整背景图")
    print("4. 文字点选验证码：保存重排序后的图片和目标文字信息")
    print("5. 所有图片保存到 captcha_images/ 目录")
