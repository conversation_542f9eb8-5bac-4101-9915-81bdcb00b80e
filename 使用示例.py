#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
东方财富网验证码加密使用示例
使用 execjs 调用 JavaScript 加密函数
"""

import execjs
import requests
import time
import json
import ddddocr
from io import BytesIO
from PIL import Image


class EastMoneyLogin:
    def __init__(self):
        # 读取加密JS文件
        with open('encrypt.js', 'r', encoding='utf-8') as f:
            js_code = f.read()
        
        # 编译JS代码
        self.ctx = execjs.compile(js_code)
        
        # 配置信息
        self.config = {
            'appid': '***********',  # 根据实际情况修改
            'api_server': 'https://smartvcode2.eastmoney.com/Titan/',
            'account': '',  # 用户账号
            'password': ''  # 用户密码
        }
        
        # 初始化验证码识别器
        self.ocr = ddddocr.DdddOcr()
        
        # 请求头
        self.headers = {
            "Accept": "*/*",
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
            "Connection": "keep-alive",
            "Referer": "https://exaccount2.eastmoney.com/",
            "Sec-Fetch-Dest": "script",
            "Sec-Fetch-Mode": "no-cors",
            "Sec-Fetch-Site": "same-site",
            "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
            "sec-ch-ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"",
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": "\"macOS\""
        }

    def get_captcha_context_id(self):
        """
        从登录页面获取 captchaContextId
        """
        # 这里需要根据实际情况实现获取 ctxid 的逻辑
        # 通常从登录页面的HTML中提取
        return "98156d95be78f8855f058463b0be37d3"  # 示例值

    def get_captcha(self, ctxid):
        """
        获取验证码
        """
        try:
            # 构建请求参数
            params = self.ctx.call('CaptchaEncrypt.buildCaptchaGetParams', 
                                 ctxid, 
                                 self.config['appid'], 
                                 self.config['account'], 
                                 self.config['password'])
            
            # 发送请求
            url = self.config['api_server'] + "api/captcha/get"
            response = requests.get(url, headers=self.headers, params=params)
            
            print(f"获取验证码响应: {response.text}")
            
            # 解析响应（去掉JSONP回调）
            json_str = response.text.replace('cb(', '').rstrip(')')
            result = json.loads(json_str)
            
            return result
            
        except Exception as e:
            print(f"获取验证码失败: {e}")
            return None

    def download_captcha_image(self, image_url):
        """
        下载验证码图片并识别
        """
        try:
            response = requests.get(image_url, headers=self.headers)
            if response.status_code == 200:
                # 使用ddddocr识别验证码
                result = self.ocr.classification(response.content)
                print(f"验证码识别结果: {result}")
                return result
            else:
                print(f"下载验证码图片失败: {response.status_code}")
                return None
        except Exception as e:
            print(f"识别验证码失败: {e}")
            return None

    def validate_captcha(self, ctxid, userresponse, data):
        """
        验证验证码
        """
        try:
            # 构建验证请求参数
            params = self.ctx.call('CaptchaEncrypt.buildCaptchaValidateParams',
                                 ctxid,
                                 self.config['appid'],
                                 self.config['account'],
                                 self.config['password'],
                                 userresponse,
                                 data)
            
            # 发送验证请求
            url = self.config['api_server'] + "api/captcha/Validate"
            response = requests.get(url, headers=self.headers, params=params)
            
            print(f"验证码验证响应: {response.text}")
            
            # 解析响应
            json_str = response.text.replace('cb(', '').rstrip(')')
            result = json.loads(json_str)
            
            return result
            
        except Exception as e:
            print(f"验证码验证失败: {e}")
            return None

    def login_process(self, account, password):
        """
        完整的登录流程
        """
        self.config['account'] = account
        self.config['password'] = password
        
        # 1. 获取验证码上下文ID
        ctxid = self.get_captcha_context_id()
        print(f"验证码上下文ID: {ctxid}")
        
        # 2. 获取验证码
        captcha_result = self.get_captcha(ctxid)
        if not captcha_result:
            return False
        
        # 检查是否需要验证码
        if captcha_result.get('Msg') == '成功':
            # 直接登录，不需要验证码
            validate_token = captcha_result.get('validate', '')
            print(f"无需验证码，直接登录。validate: {validate_token}")
            return self.final_login(ctxid, validate_token)
        
        elif captcha_result.get('Msg') == '升级':
            # 需要验证码
            print("需要验证码验证")
            
            # 3. 下载并识别验证码
            if 'CaptchaImageUrl' in captcha_result:
                captcha_text = self.download_captcha_image(captcha_result['CaptchaImageUrl'])
                if not captcha_text:
                    return False
                
                # 4. 验证验证码
                validate_result = self.validate_captcha(ctxid, captcha_text, captcha_result.get('data', ''))
                
                if validate_result and validate_result.get('Msg') == '成功':
                    validate_token = validate_result.get('validate', '')
                    print(f"验证码验证成功。validate: {validate_token}")
                    return self.final_login(ctxid, validate_token)
                else:
                    print("验证码验证失败")
                    return False
            else:
                print("未找到验证码图片URL")
                return False
        
        else:
            print(f"获取验证码失败: {captcha_result.get('Msg', '未知错误')}")
            return False

    def final_login(self, ctxid, validate_token):
        """
        最终登录步骤
        """
        try:
            # 构建登录请求
            login_url = "https://exaccount2.eastmoney.com/JsonAPI/Login3"
            login_data = {
                "username": self.config['account'],
                "password": self.config['password'],  # 这里可能需要RSA加密
                "captconetxt": ctxid,
                "captvalidate": validate_token
            }
            
            login_headers = {
                "Accept": "application/json, text/javascript, */*; q=0.01",
                "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
                "Connection": "keep-alive",
                "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
                "Origin": "https://exaccount2.eastmoney.com",
                "Referer": "https://exaccount2.eastmoney.com/home/<USER>",
                "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
                "X-Requested-With": "XMLHttpRequest"
            }
            
            response = requests.post(login_url, headers=login_headers, data=login_data)
            print(f"登录响应: {response.text}")
            
            return response.status_code == 200
            
        except Exception as e:
            print(f"最终登录失败: {e}")
            return False


def main():
    """
    主函数 - 使用示例
    """
    # 创建登录实例
    login = EastMoneyLogin()
    
    # 设置账号密码
    account = "***********"  # 替换为实际账号
    password = "your_password"  # 替换为实际密码
    
    # 执行登录流程
    success = login.login_process(account, password)
    
    if success:
        print("登录成功！")
    else:
        print("登录失败！")


if __name__ == "__main__":
    main()
