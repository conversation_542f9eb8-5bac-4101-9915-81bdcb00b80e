#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试东方财富网加密函数
"""

import execjs
import time


def test_encrypt():
    """
    测试加密函数
    """
    try:
        # 读取加密JS文件
        with open('encrypt.js', 'r', encoding='utf-8') as f:
            js_code = f.read()
        
        # 编译JS代码
        ctx = execjs.compile(js_code)
        
        # 测试参数
        test_params = {
            'appid': '***********',
            'ctxid': '98156d95be78f8855f058463b0be37d3',
            'account': '***********',
            'password': 'testpassword'
        }
        
        print("=== 测试加密函数 ===")
        
        # 1. 测试基础加密函数
        test_string = "appid=***********|ctxid=98156d95be78f8855f058463b0be37d3|a=***********|p=testpassword|r=0.*********"
        encrypted = ctx.call('CaptchaEncrypt.encrypt', test_string)
        print(f"原始字符串: {test_string}")
        print(f"加密结果: {encrypted}")
        
        # 2. 测试Base64编码
        base64_result = ctx.call('CaptchaEncrypt.base64Encode', encrypted)
        print(f"Base64编码: {base64_result}")
        
        # 3. 测试生成请求数据
        request_data = ctx.call('CaptchaEncrypt.generateRequestData', 
                               test_params['appid'], 
                               test_params['ctxid'], 
                               test_params['account'], 
                               test_params['password'])
        print(f"生成的请求数据: {request_data}")
        
        # 4. 测试获取时间戳
        timestamp = ctx.call('CaptchaEncrypt.getTimestamp')
        print(f"时间戳: {timestamp}")
        
        # 5. 测试构建验证码获取参数
        get_params = ctx.call('CaptchaEncrypt.buildCaptchaGetParams',
                             test_params['ctxid'],
                             test_params['appid'],
                             test_params['account'],
                             test_params['password'])
        print(f"验证码获取参数: {get_params}")
        
        # 6. 测试构建验证码验证参数
        validate_params = ctx.call('CaptchaEncrypt.buildCaptchaValidateParams',
                                  test_params['ctxid'],
                                  test_params['appid'],
                                  test_params['account'],
                                  test_params['password'],
                                  'test_response',
                                  'test_data')
        print(f"验证码验证参数: {validate_params}")
        
        print("\n=== 测试完成 ===")
        return True
        
    except Exception as e:
        print(f"测试失败: {e}")
        return False


def compare_with_original():
    """
    与原始加密结果对比
    """
    try:
        with open('encrypt.js', 'r', encoding='utf-8') as f:
            js_code = f.read()
        
        ctx = execjs.compile(js_code)
        
        # 使用与原始请求相同的参数进行测试
        original_ctxid = "98156d95be78f8855f058463b0be37d3"
        original_appid = "***********"
        
        # 生成请求参数
        params = ctx.call('CaptchaEncrypt.buildCaptchaGetParams',
                         original_ctxid,
                         original_appid,
                         "***********",
                         "testpassword")
        
        print("=== 对比测试 ===")
        print(f"生成的参数:")
        print(f"  callback: {params['callback']}")
        print(f"  ctxid: {params['ctxid']}")
        print(f"  request: {params['request']}")
        print(f"  _: {params['_']}")
        
        # 原始请求中的request值（用于对比）
        original_request = "ZKk+3tHjraXNmZ2vaCtKwLuCClOBe08xbBK6ImY5WBzfnKrnOymdKu2x073g+gHq5uqCpSH+WQYkr6o51llrt9xkVKTQhLGLJo2Rk0Rk6aJ0vVNpU1suhIOmtitbFZsv3I6eJPQZMBES1lMdASiOkg=="
        print(f"\n原始request值: {original_request}")
        print(f"生成request值: {params['request']}")
        
        # 注意：由于包含随机数，每次生成的request值都会不同，这是正常的
        print("\n注意：由于加密字符串中包含随机数，每次生成的request值都会不同")
        
        return True
        
    except Exception as e:
        print(f"对比测试失败: {e}")
        return False


if __name__ == "__main__":
    print("开始测试东方财富网加密函数...")
    
    # 基础功能测试
    if test_encrypt():
        print("✓ 基础功能测试通过")
    else:
        print("✗ 基础功能测试失败")
    
    print("\n" + "="*50 + "\n")
    
    # 对比测试
    if compare_with_original():
        print("✓ 对比测试完成")
    else:
        print("✗ 对比测试失败")
