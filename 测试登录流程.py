#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试东方财富网登录流程的各个步骤
用于调试和验证每个步骤是否正常工作
"""

import requests
import  execjs
import json
import time
from bs4 import BeautifulSoup


class LoginFlowTester:
    def __init__(self):
        # 读取加密JS文件
        with open('encrypt.js', 'r', encoding='utf-8') as f:
            js_code = f.read()
        
        # 编译JS代码
        self.ctx = execjs.compile(js_code)
        
        # 会话对象
        self.session = requests.Session()
        
        # 基础配置
        self.config = {
            'appid': '***********1',  # 正确的AppId
            'login_page_url': 'https://exaccount2.eastmoney.com/home/<USER>',
            'captcha_api_base': 'https://smartvcode2.eastmoney.com/Titan/api/captcha/'
        }
        
        # 请求头
        self.headers = {
            "Accept": "*/*",
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
            "Connection": "keep-alive",
            "Referer": "https://exaccount2.eastmoney.com/",
            "Sec-Fetch-Dest": "script",
            "Sec-Fetch-Mode": "no-cors",
            "Sec-Fetch-Site": "same-site",
            "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
            "sec-ch-ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"",
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": "\"macOS\""
        }

    def test_step1_get_ctxid(self):
        """
        测试步骤1: 获取验证码上下文ID
        """
        print("=== 测试步骤1: 获取验证码上下文ID ===")
        
        try:
            # 构建登录页面URL
            params = {'rc': str(int(time.time() * 1000))}
            
            # 设置页面请求头
            page_headers = {
                "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
                "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
                "Connection": "keep-alive",
                "Referer": "https://passport2.eastmoney.com/",
                "Sec-Fetch-Dest": "iframe",
                "Sec-Fetch-Mode": "navigate",
                "Sec-Fetch-Site": "same-site",
                "Upgrade-Insecure-Requests": "1",
                "User-Agent": self.headers["User-Agent"]
            }
            
            # 请求登录页面
            response = self.session.get(self.config['login_page_url'], 
                                      headers=page_headers, 
                                      params=params)
            
            print(f"登录页面状态码: {response.status_code}")
            
            if response.status_code == 200:
                # 解析HTML获取ctxid
                soup = BeautifulSoup(response.text, 'html.parser')
                input_element = soup.find('input', {'id': 'hdAccountCaptContextId'})
                
                if input_element:
                    ctxid = input_element.get('value')
                    print(f"✅ 成功获取ctxid: {ctxid}")
                    return ctxid
                else:
                    print("❌ 未找到hdAccountCaptContextId元素")
                    # 打印部分HTML内容用于调试
                    print("页面内容片段:")
                    print(response.text[:1000])
                    return None
            else:
                print(f"❌ 获取登录页面失败: {response.status_code}")
                return None
                
        except Exception as e:
            print(f"❌ 步骤1失败: {e}")
            return None

    def test_step2_first_captcha_get(self, ctxid):
        """
        测试步骤2: 第一次获取验证码
        """
        print("=== 测试步骤2: 第一次获取验证码 ===")
        
        if not ctxid:
            print("❌ 缺少ctxid参数")
            return None
        
        try:
            # 测试参数
            account = "***********"
            password = "123456"
            
            # 构建请求参数
            params = self.ctx.call('CaptchaEncrypt.buildCaptchaGetParams',
                                 ctxid,
                                 self.config['appid'],
                                 account,
                                 password)
            
            print(f"请求参数: {params}")
            
            # 发送请求
            url = self.config['captcha_api_base'] + "get"
            response = self.session.get(url, headers=self.headers, params=params)
            
            print(f"响应状态码: {response.status_code}")
            print(f"响应内容: {response.text}")
            
            if response.status_code == 200:
                # 解析JSONP响应
                json_str = response.text.replace('cb(', '').rstrip(')')
                try:
                    result = json.loads(json_str)
                    print(f"✅ 第一次获取验证码成功")
                    return result
                except json.JSONDecodeError as e:
                    print(f"❌ JSON解析失败: {e}")
                    print(f"原始响应: {response.text}")
                    return None
            else:
                print(f"❌ 第一次获取验证码失败: {response.status_code}")
                return None
                
        except Exception as e:
            print(f"❌ 步骤2失败: {e}")
            return None

    def test_step3_first_validate(self, ctxid):
        """
        测试步骤3: 第一次验证
        """
        print("=== 测试步骤3: 第一次验证 ===")
        
        if not ctxid:
            print("❌ 缺少ctxid参数")
            return None
        
        try:
            # 测试参数
            account = "test_account"
            password = "test_password"
            
            # 构建验证请求参数（第一次验证不需要用户输入）
            params = self.ctx.call('CaptchaEncrypt.buildCaptchaValidateParams',
                                 ctxid,
                                 self.config['appid'],
                                 account,
                                 password,
                                 '',  # 第一次验证无用户响应
                                 '')  # 第一次验证无数据
            
            print(f"请求参数: {params}")
            
            # 发送验证请求
            url = self.config['captcha_api_base'] + "Validate"
            response = self.session.get(url, headers=self.headers, params=params)
            
            print(f"响应状态码: {response.status_code}")
            print(f"响应内容: {response.text}")
            
            if response.status_code == 200:
                # 解析响应
                json_str = response.text.replace('cb(', '').rstrip(')')
                try:
                    result = json.loads(json_str)
                    print(f"✅ 第一次验证完成")
                except json.JSONDecodeError as e:
                    print(f"❌ JSON解析失败: {e}")
                    print(f"原始响应: {response.text}")
                    return None
                
                # 分析响应结果
                msg = result.get('Msg', '')
                if msg == '成功':
                    print("🎉 第一次验证成功，无需验证码！")
                    validate_token = result.get('validate', '')
                    print(f"获取到validate token: {validate_token}")
                elif msg == '升级':
                    print("⚠️  需要进行验证码验证")
                else:
                    print(f"⚠️  未知响应状态: {msg}")
                
                return result
            else:
                print(f"❌ 第一次验证失败: {response.status_code}")
                return None
                
        except Exception as e:
            print(f"❌ 步骤3失败: {e}")
            return None

    def test_encryption_functions(self):
        """
        测试加密函数
        """
        print("=== 测试加密函数 ===")
        
        try:
            # 测试参数
            test_ctxid = "test_ctxid_123456"
            test_appid = "***********"
            test_account = "test_account"
            test_password = "test_password"
            
            # 测试生成请求数据
            request_data = self.ctx.call('CaptchaEncrypt.generateRequestData',
                                       test_appid,
                                       test_ctxid,
                                       test_account,
                                       test_password)
            print(f"✅ 生成请求数据: {request_data[:50]}...")
            
            # 测试构建get参数
            get_params = self.ctx.call('CaptchaEncrypt.buildCaptchaGetParams',
                                     test_ctxid,
                                     test_appid,
                                     test_account,
                                     test_password)
            print(f"✅ 构建get参数: {get_params}")
            
            # 测试构建validate参数
            validate_params = self.ctx.call('CaptchaEncrypt.buildCaptchaValidateParams',
                                          test_ctxid,
                                          test_appid,
                                          test_account,
                                          test_password,
                                          'test_response',
                                          'test_data')
            print(f"✅ 构建validate参数: {validate_params}")
            
            return True
            
        except Exception as e:
            print(f"❌ 加密函数测试失败: {e}")
            return False

    def run_all_tests(self):
        """
        运行所有测试
        """
        print("开始测试东方财富网登录流程各个步骤")
        print("=" * 60)
        
        # 测试加密函数
        if not self.test_encryption_functions():
            print("❌ 加密函数测试失败，停止后续测试")
            return
        
        print("\n" + "-" * 40 + "\n")
        
        # 测试步骤1: 获取ctxid
        ctxid = self.test_step1_get_ctxid()
        if not ctxid:
            print("❌ 步骤1失败，停止后续测试")
            return
        
        print("\n" + "-" * 40 + "\n")
        
        # 测试步骤2: 第一次获取验证码
        first_get_result = self.test_step2_first_captcha_get(ctxid)
        if not first_get_result:
            print("❌ 步骤2失败，停止后续测试")
            return
        
        print("\n" + "-" * 40 + "\n")
        
        # 测试步骤3: 第一次验证
        first_validate_result = self.test_step3_first_validate(ctxid)
        if not first_validate_result:
            print("❌ 步骤3失败")
            return
        
        print("\n" + "=" * 60)
        print("🎉 所有基础步骤测试完成！")
        
        # 总结测试结果
        print("\n=== 测试总结 ===")
        print(f"1. 获取ctxid: ✅ {ctxid}")
        print(f"2. 第一次获取验证码: ✅ 成功")
        print(f"3. 第一次验证: ✅ 响应状态: {first_validate_result.get('Msg', '未知')}")
        
        if first_validate_result.get('Msg') == '成功':
            print("   → 可以直接登录，无需验证码")
        elif first_validate_result.get('Msg') == '升级':
            print("   → 需要验证码验证，可以继续后续步骤")
        
        print("\n💡 提示: 如果需要测试验证码相关步骤，请使用真实的账号密码")


def main():
    """
    主函数
    """
    tester = LoginFlowTester()
    tester.run_all_tests()


if __name__ == "__main__":
    main()
