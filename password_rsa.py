#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
东方财富网密码RSA加密模块
"""

import base64
from Crypto.PublicKey import RSA
from Crypto.Cipher import PKCS1_v1_5
import binascii


class EastMoneyPasswordEncryptor:
    def __init__(self):
        # 东方财富网的RSA公钥
        self.public_key_str = """MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDBgxenWGQrynpHxvRsnlXWBFCrGhf3eES3/aajLV+oceh1m4xZyUSA5mMoRvdvfmo+snVPuGPTwzz4MP1xLSgEtcQRzl1atza0Kt106HBKihKqhqJsLTSRE0xiGcZJMPpcpho/xLI+T3nmsHwQTMQD+TAgmzLBnffs6Hoart6FPQIDAQAB"""
        
        # 构建完整的PEM格式公钥
        self.pem_public_key = f"""-----BEGIN PUBLIC KEY-----
{self.public_key_str}
-----END PUBLIC KEY-----"""
        
        # 导入RSA公钥
        try:
            self.rsa_key = RSA.import_key(self.pem_public_key)
            self.cipher = PKCS1_v1_5.new(self.rsa_key)
        except Exception as e:
            print(f"RSA密钥导入失败: {e}")
            self.rsa_key = None
            self.cipher = None

    def encrypt_password(self, password):
        """
        使用RSA公钥加密密码
        """
        if not self.cipher:
            print("RSA加密器未初始化")
            return None
        
        try:
            # 将密码转换为字节
            password_bytes = password.encode('utf-8')
            
            # RSA加密
            encrypted_bytes = self.cipher.encrypt(password_bytes)
            
            # Base64编码
            encrypted_base64 = base64.b64encode(encrypted_bytes).decode('utf-8')
            
            return encrypted_base64
            
        except Exception as e:
            print(f"密码加密失败: {e}")
            return None

    def test_encryption(self):
        """
        测试加密功能
        """
        test_password = "123456"
        encrypted = self.encrypt_password(test_password)
        
        if encrypted:
            print(f"测试密码: {test_password}")
            print(f"加密结果: {encrypted}")
            print(f"加密结果长度: {len(encrypted)}")
            return True
        else:
            print("加密测试失败")
            return False


def encrypt_password(password):
    """
    便捷函数：加密密码
    """
    encryptor = EastMoneyPasswordEncryptor()
    return encryptor.encrypt_password(password)


def main():
    """
    测试函数
    """
    print("=== 东方财富网密码RSA加密测试 ===")
    
    encryptor = EastMoneyPasswordEncryptor()
    
    # 测试加密
    if encryptor.test_encryption():
        print("✅ RSA加密功能正常")
    else:
        print("❌ RSA加密功能异常")
    
    # 测试多个密码
    test_passwords = ["123456", "password", "test123", "18888888888"]
    
    print("\n=== 批量加密测试 ===")
    for pwd in test_passwords:
        encrypted = encryptor.encrypt_password(pwd)
        if encrypted:
            print(f"密码: {pwd} -> 加密: {encrypted[:50]}...")
        else:
            print(f"密码: {pwd} -> 加密失败")


if __name__ == "__main__":
    main()
