流程分析：
输入完账号密码后点击验证，先执行 ‘get?callback=cb&ctxid=bb5a7976c6bb5e42e94582bdc5b6d4a6’。
在 ‘get?callback=cb&ctxid=bb5a7976c6bb5e42e94582bdc5b6d4a6’中负载的 ‘ctxid‘ 来自‘Login4?rc=9’网页源代码中的 'id': 'hdAccountCaptContextId'
随后执行 ‘Validate?callback=cb&ctxid=bb5a7976c6bb5e42e94582bdc5b6d4a6’ 如果响应结果中存在 ‘ Msg: "升级" ’，则触发第二次验证：验证码验证，
执行 ‘get?callback=cb&ctxid=bb5a7976c6bb5e42e94582bdc5b6d4a6‘ ，验证后执行 ‘‘Validate?callback=cb&ctxid=bb5a7976c6bb5e42e94582bdc5b6d4a6‘ ，如果如果响应结果中存在 ‘ Msg: "成功" ’，则将响应信息里的"validate\":\"645973bac38377cb727f40f50102f5b4\"的 ‘645973bac38377cb727f40f50102f5b4’ 值传入 ’Login3'负载的 ‘captvalidate’， ‘Login3’ 中负载的 ‘ctxid‘ 来自网页源代码中的 'id': 'hdAccountCaptContextId' 随后执行
如果第一开始‘Validate?callback=cb&ctxid=bb5a7976c6bb5e42e94582bdc5b6d4a6’响应结果中存在 ‘Msg: "成功" ’ 则将响应信息里的"validate\":\"645973bac38377cb727f40f50102f5b4\"的 ‘645973bac38377cb727f40f50102f5b4’ 值传入 ’Login3'负载的 ‘captvalidate’， ‘Login3’ 中负载的 ‘ctxid‘ 来自网页源代码中的 'id': 'hdAccountCaptContextId' 随后执行
如果第一开始‘Validate?callback=cb&ctxid=bb5a7976c6bb5e42e94582bdc5b6d4a6’响应结果中存在 ‘Msg: "成功" ’ 则执行 ‘Login3’


利用 ‘ddddocr’ 进行自动识别验证码，至于验证码类型，在请求 captcha/get的响应结果里会显示是什么类型的验证码 "CaptchaType；"
‘ api/captcha/get ‘ 和 'api/captcha/Validate'的请求都存在 ’_ ‘的时间戳，并且他们的'request'加密逻辑是一样的
‘ Login3 ’ 中  'captconetxt‘ = ’ctxid‘ ； ‘captvalidate’ = ‘validate’
加密算法为JS文件，通过 ‘import execjs’ 进行调用

