# 东方财富网验证码处理功能

## 🎯 功能概述

东方财富网的验证码有两种类型，并且图片都是**乱序的**，需要先重新排序组合成正常图片才能进行识别和处理。

### 验证码类型

1. **滑块验证码 (slide)**
   - 需要计算滑动距离
   - 包含：背景图（有缺口）、缺口图片、完整背景图
   - 返回：滑动距离（像素值）

2. **文字点选验证码 (click)**
   - 需要识别目标文字位置
   - 包含：验证码图片、目标文字列表
   - 返回：点击坐标（格式：x1,y1|x2,y2|x3,y3）

## 🔧 核心功能

### 1. 图片重排序算法

```python
def reorder_captcha_image(self, image_data, captcha_info):
    """
    重新排序验证码图片
    东方财富网的验证码图片是乱序的，需要根据captcha_info中的排序信息重新组合
    """
```

**原理说明：**
- 验证码图片被分割成小块（如2行5列，共10块）
- `order` 数组记录了每个位置应该放置哪个原始块
- 算法将乱序的块按正确顺序重新组合

**示例：**
```json
{
  "order": [4, 1, 8, 2, 6, 0, 7, 3, 9, 5],
  "rows": 2,
  "cols": 5
}
```

### 2. 验证码图片保存

```python
def save_captcha_images(self, captcha_info, captcha_type):
    """
    根据验证码类型下载并保存相关图片
    """
```

**滑块验证码保存：**
- `slide_bg_时间戳.jpg` - 重排序后的背景图（有缺口）
- `slide_patch_时间戳.png` - 缺口图片
- `slide_fullbg_时间戳.jpg` - 重排序后的完整背景图

**文字点选验证码保存：**
- `click_image_时间戳.jpg` - 重排序后的验证码图片
- `click_words_时间戳.txt` - 目标文字信息

### 3. 验证码识别

```python
def download_and_recognize_captcha(self, image_url, captcha_type='unknown', target_words='', captcha_info_str=''):
    """
    下载验证码图片并识别
    """
```

**处理流程：**
1. 解析验证码信息
2. 保存相关图片（重排序后）
3. 根据类型进行识别
4. 返回识别结果

## 📁 文件结构

```
captcha_images/
├── slide_bg_20250728_143022.jpg      # 滑块背景图
├── slide_patch_20250728_143022.png   # 滑块缺口图
├── slide_fullbg_20250728_143022.jpg  # 滑块完整背景图
├── click_image_20250728_143022.jpg   # 点选验证码图片
└── click_words_20250728_143022.txt   # 点选目标文字
```

## 🚀 使用方法

### 基本用法

```python
from 完整登录流程 import EastMoneyLoginFlow
import json

# 创建实例
login_flow = EastMoneyLoginFlow()

# 验证码信息（从API响应中获取）
captcha_info = {
    "type": "slide",
    "static_servers": ["smartvcode2.eastmoney.com"],
    "bg": "/path/to/bg.jpg",
    "patch": "/path/to/patch.png", 
    "fullbg": "/path/to/fullbg.jpg",
    "order": [4, 1, 8, 2, 6, 0, 7, 3, 9, 5],
    "rows": 2,
    "cols": 5
}

# 识别验证码
result = login_flow.download_and_recognize_captcha(
    image_url="https://smartvcode2.eastmoney.com/path/to/image.jpg",
    captcha_type="slide",
    target_words=[],
    captcha_info_str=json.dumps(captcha_info)
)

# 处理结果
if captcha_info["type"] == "slide":
    slide_distance = int(result)
    print(f"滑动距离: {slide_distance}px")
elif captcha_info["type"] == "click":
    coordinates = result.split("|")
    print(f"点击坐标: {coordinates}")
```

### 集成到登录流程

验证码处理功能已经完全集成到 `完整登录流程.py` 中，在需要验证码时会自动：

1. 识别验证码类型
2. 下载并重排序图片
3. 保存到本地文件
4. 进行识别处理
5. 返回识别结果

## 🔍 技术细节

### 图片重排序算法

```python
# 按照order数组重新排列图片块
for i, original_pos in enumerate(order):
    # 计算当前位置在新图中的坐标
    new_row = i // cols
    new_col = i % cols
    new_x = new_col * block_width
    new_y = new_row * block_height
    
    # 计算原始位置的坐标
    orig_row = original_pos // cols
    orig_col = original_pos % cols
    orig_x = orig_col * block_width
    orig_y = orig_row * block_height
    
    # 提取并粘贴图片块
    block = original_image.crop((orig_x, orig_y, orig_x + block_width, orig_y + block_height))
    reordered_image.paste(block, (new_x, new_y))
```

### 依赖库

- `Pillow` - 图像处理
- `requests` - 网络请求
- `json` - JSON数据处理
- `ddddocr` - OCR识别（可选）

## 🎉 功能特点

✅ **自动识别验证码类型** - 支持slide和click两种类型  
✅ **图片重排序算法** - 解决乱序图片问题  
✅ **完整图片保存** - 保存所有相关图片文件  
✅ **集成登录流程** - 无缝集成到完整登录流程  
✅ **错误处理机制** - 完善的异常处理  
✅ **调试信息输出** - 详细的处理过程日志  

## 📝 注意事项

1. 确保安装了 `Pillow` 库：`pip install Pillow`
2. 验证码图片URL可能会过期，需要及时处理
3. 图片重排序依赖于 `order` 数组的准确性
4. 滑块验证码的距离计算可能需要更精确的算法
5. 文字点选验证码的坐标识别可能需要OCR优化

## 🔧 扩展功能

可以进一步扩展的功能：

- 使用深度学习模型进行滑块距离计算
- 优化OCR识别准确率
- 添加验证码缓存机制
- 支持更多验证码类型
- 添加图片预处理功能（去噪、增强等）
